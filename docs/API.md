# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    }
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "错误信息",
    "data": null
}
```

### 分页响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [],
        "total": 100,
        "page": 1,
        "page_size": 10,
        "total_pages": 10
    }
}
```

## 前台展示接口

### 1. 获取作品列表

**接口地址**: `GET /portfolios`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10 |
| category_id | int | 否 | 分类ID |
| keyword | string | 否 | 搜索关键词 |

**请求示例**:
```bash
GET /api/v1/portfolios?page=1&page_size=10&category_id=1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "id": 1,
                "title": "网站设计作品",
                "description": "这是一个现代化的网站设计作品",
                "cover_image": "/uploads/cover1.jpg",
                "category": {
                    "id": 1,
                    "name": "网页设计"
                },
                "view_count": 156,
                "created_at": "2024-01-15T10:30:00Z"
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10,
        "total_pages": 3
    }
}
```

### 2. 获取作品详情

**接口地址**: `GET /portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

**请求示例**:
```bash
GET /api/v1/portfolios/1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "title": "网站设计作品",
        "description": "这是一个现代化的网站设计作品",
        "content": "详细的作品介绍内容...",
        "cover_image": "/uploads/cover1.jpg",
        "images": [
            "/uploads/image1.jpg",
            "/uploads/image2.jpg",
            "/uploads/image3.jpg"
        ],
        "category": {
            "id": 1,
            "name": "网页设计"
        },
        "tags": "网页设计,UI设计,响应式",
        "view_count": 157,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }
}
```

### 3. 获取分类列表

**接口地址**: `GET /categories`

**请求示例**:
```bash
GET /api/v1/categories
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "网页设计",
            "description": "网站和网页相关的设计作品",
            "sort_order": 1
        },
        {
            "id": 2,
            "name": "移动应用",
            "description": "手机APP和移动端设计作品",
            "sort_order": 2
        }
    ]
}
```

### 4. 获取推荐作品

**接口地址**: `GET /portfolios/featured`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | int | 否 | 数量限制，默认6 |

**请求示例**:
```bash
GET /api/v1/portfolios/featured?limit=6
```

## 认证接口

### 1. 管理员登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
    "username": "admin",
    "password": "password123"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        },
        "expires_at": "2024-01-16T10:30:00Z"
    }
}
```

### 2. 获取用户信息

**接口地址**: `GET /auth/profile`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "avatar": "/uploads/avatar.jpg",
        "role": "admin",
        "created_at": "2024-01-01T00:00:00Z"
    }
}
```

### 3. 登出

**接口地址**: `POST /auth/logout`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

## 后台管理接口

> 以下接口均需要管理员权限，请求头需要包含有效的JWT Token

### 1. 获取作品管理列表

**接口地址**: `GET /admin/portfolios`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10 |
| status | string | 否 | 状态筛选：draft/published |
| category_id | int | 否 | 分类ID |

**请求头**:
```
Authorization: Bearer {token}
```

### 2. 创建作品

**接口地址**: `POST /admin/portfolios`

**请求参数**:
```json
{
    "title": "新作品标题",
    "description": "作品简介",
    "content": "详细内容",
    "cover_image": "/uploads/cover.jpg",
    "images": [
        "/uploads/image1.jpg",
        "/uploads/image2.jpg"
    ],
    "category_id": 1,
    "tags": "标签1,标签2,标签3",
    "status": "published",
    "sort_order": 0
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "作品创建成功",
    "data": {
        "id": 10,
        "title": "新作品标题",
        "description": "作品简介",
        "cover_image": "/uploads/cover.jpg",
        "status": "published",
        "created_at": "2024-01-15T10:30:00Z"
    }
}
```

### 3. 更新作品

**接口地址**: `PUT /admin/portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

**请求参数**:
```json
{
    "title": "更新后的标题",
    "description": "更新后的简介",
    "status": "published"
}
```

### 4. 删除作品

**接口地址**: `DELETE /admin/portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

**响应示例**:
```json
{
    "code": 200,
    "message": "作品删除成功",
    "data": null
}
```

### 5. 分类管理

#### 获取分类列表
**接口地址**: `GET /admin/categories`

#### 创建分类
**接口地址**: `POST /admin/categories`

**请求参数**:
```json
{
    "name": "新分类",
    "description": "分类描述",
    "sort_order": 1
}
```

#### 更新分类
**接口地址**: `PUT /admin/categories/{id}`

#### 删除分类
**接口地址**: `DELETE /admin/categories/{id}`

## 文件上传接口

### 1. 单图片上传

**接口地址**: `POST /upload/image`

**请求方式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | file | 是 | 图片文件 |

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/api/v1/upload/image \
  -H 'Authorization: Bearer {token}' \
  -F 'image=@/path/to/image.jpg'
```

**响应示例**:
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "url": "/uploads/2024/01/15/image_123456.jpg",
        "filename": "image_123456.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg"
    }
}
```

### 2. 批量图片上传

**接口地址**: `POST /upload/multiple`

**请求方式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| images | file[] | 是 | 多个图片文件 |

**响应示例**:
```json
{
    "code": 200,
    "message": "批量上传成功",
    "data": {
        "success": [
            {
                "url": "/uploads/2024/01/15/image1_123456.jpg",
                "filename": "image1_123456.jpg"
            },
            {
                "url": "/uploads/2024/01/15/image2_123457.jpg",
                "filename": "image2_123457.jpg"
            }
        ],
        "failed": []
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 请求示例

### 使用curl
```bash
# 获取作品列表
curl -X GET "http://localhost:8080/api/v1/portfolios?page=1&page_size=10"

# 管理员登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# 创建作品（需要认证）
curl -X POST "http://localhost:8080/api/v1/admin/portfolios" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"title":"新作品","description":"作品描述","category_id":1}'
```

### 使用JavaScript (Axios)
```javascript
// 设置默认请求头
axios.defaults.baseURL = 'http://localhost:8080/api/v1';
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

// 获取作品列表
const portfolios = await axios.get('/portfolios', {
    params: { page: 1, page_size: 10 }
});

// 创建作品
const newPortfolio = await axios.post('/admin/portfolios', {
    title: '新作品',
    description: '作品描述',
    category_id: 1
});
```
