package service

import (
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

// SessionInfo 会话信息
type SessionInfo struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Username  string    `json:"username"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	LastUsed  time.Time `json:"last_used"`
}

// SessionService 会话服务接口
type SessionService interface {
	CreateSession(userID, username string, expireHours int) (*SessionInfo, error)
	GetSession(sessionID string) (*SessionInfo, error)
	UpdateLastUsed(sessionID string) error
	DeleteSession(sessionID string) error
	CleanExpiredSessions() int
	IsValidSession(sessionID string) bool
}

// sessionService 会话服务实现（内存存储）
type sessionService struct {
	sessions map[string]*SessionInfo
	mutex    sync.RWMutex
}

// NewSessionService 创建会话服务
func NewSessionService() SessionService {
	service := &sessionService{
		sessions: make(map[string]*SessionInfo),
	}
	
	// 启动清理过期会话的定时任务
	go service.cleanupExpiredSessions()
	
	return service
}

// CreateSession 创建会话
func (s *sessionService) CreateSession(userID, username string, expireHours int) (*SessionInfo, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	sessionID := uuid.New().String()
	now := time.Now()
	
	session := &SessionInfo{
		ID:        sessionID,
		UserID:    userID,
		Username:  username,
		CreatedAt: now,
		ExpiresAt: now.Add(time.Duration(expireHours) * time.Hour),
		LastUsed:  now,
	}
	
	s.sessions[sessionID] = session
	
	return session, nil
}

// GetSession 获取会话
func (s *sessionService) GetSession(sessionID string) (*SessionInfo, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("会话不存在")
	}
	
	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		// 删除过期会话
		delete(s.sessions, sessionID)
		return nil, fmt.Errorf("会话已过期")
	}
	
	return session, nil
}

// UpdateLastUsed 更新最后使用时间
func (s *sessionService) UpdateLastUsed(sessionID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return fmt.Errorf("会话不存在")
	}
	
	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		delete(s.sessions, sessionID)
		return fmt.Errorf("会话已过期")
	}
	
	session.LastUsed = time.Now()
	return nil
}

// DeleteSession 删除会话
func (s *sessionService) DeleteSession(sessionID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	delete(s.sessions, sessionID)
	return nil
}

// CleanExpiredSessions 清理过期会话
func (s *sessionService) CleanExpiredSessions() int {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	now := time.Now()
	count := 0
	
	for sessionID, session := range s.sessions {
		if now.After(session.ExpiresAt) {
			delete(s.sessions, sessionID)
			count++
		}
	}
	
	return count
}

// IsValidSession 检查会话是否有效
func (s *sessionService) IsValidSession(sessionID string) bool {
	_, err := s.GetSession(sessionID)
	return err == nil
}

// cleanupExpiredSessions 定时清理过期会话
func (s *sessionService) cleanupExpiredSessions() {
	ticker := time.NewTicker(30 * time.Minute) // 每30分钟清理一次
	defer ticker.Stop()
	
	for range ticker.C {
		count := s.CleanExpiredSessions()
		if count > 0 {
			fmt.Printf("清理了 %d 个过期会话\n", count)
		}
	}
}

// GetSessionCount 获取当前会话数量
func (s *sessionService) GetSessionCount() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	return len(s.sessions)
}

// GetAllSessions 获取所有会话（管理用）
func (s *sessionService) GetAllSessions() []*SessionInfo {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	sessions := make([]*SessionInfo, 0, len(s.sessions))
	for _, session := range s.sessions {
		sessions = append(sessions, session)
	}
	
	return sessions
}

// ExtendSession 延长会话有效期
func (s *sessionService) ExtendSession(sessionID string, expireHours int) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	session, exists := s.sessions[sessionID]
	if !exists {
		return fmt.Errorf("会话不存在")
	}
	
	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		delete(s.sessions, sessionID)
		return fmt.Errorf("会话已过期")
	}
	
	// 延长有效期
	session.ExpiresAt = time.Now().Add(time.Duration(expireHours) * time.Hour)
	session.LastUsed = time.Now()
	
	return nil
}

// 全局会话服务实例
var globalSessionService SessionService

// InitSessionService 初始化会话服务
func InitSessionService() {
	globalSessionService = NewSessionService()
}

// GetSessionService 获取全局会话服务
func GetSessionService() SessionService {
	if globalSessionService == nil {
		InitSessionService()
	}
	return globalSessionService
}
