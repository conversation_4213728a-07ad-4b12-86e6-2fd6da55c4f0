package middleware

import (
	"net/http"
	"portfolio-website/internal/config"
	"portfolio-website/internal/model"
	"portfolio-website/internal/service"
	"strings"

	"github.com/gin-gonic/gin"
)

// AdminAuth 管理员认证中间件
func AdminAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Cookie或Header获取session_id
		sessionID := getSessionID(c)
		if sessionID == "" {
			c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "未提供认证信息"))
			c.Abort()
			return
		}
		
		// 验证session（这里简化处理，实际应该从缓存或数据库验证）
		if !isValidSession(sessionID, cfg) {
			c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "认证信息无效或已过期"))
			c.Abort()
			return
		}
		
		// 设置用户信息到上下文
		c.Set("admin", true)
		c.Set("session_id", sessionID)
		
		c.Next()
	}
}

// getSessionID 从请求中获取session ID
func getSessionID(c *gin.Context) string {
	// 首先尝试从Authorization头获取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// 支持Bearer token格式
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
		return authHeader
	}
	
	// 然后尝试从Cookie获取
	sessionID, err := c.Cookie("session_id")
	if err == nil && sessionID != "" {
		return sessionID
	}
	
	// 最后尝试从查询参数获取（不推荐，仅用于调试）
	return c.Query("session_id")
}

// isValidSession 验证session是否有效
func isValidSession(sessionID string, cfg *config.Config) bool {
	sessionService := service.GetSessionService()

	// 验证会话是否存在且有效
	session, err := sessionService.GetSession(sessionID)
	if err != nil {
		return false
	}

	// 更新最后使用时间
	sessionService.UpdateLastUsed(session.ID)

	return true
}

// RequireAuth 通用认证中间件
func RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否已认证
		if !isAuthenticated(c) {
			c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "需要认证"))
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// isAuthenticated 检查是否已认证
func isAuthenticated(c *gin.Context) bool {
	// 检查上下文中是否有认证信息
	if admin, exists := c.Get("admin"); exists && admin.(bool) {
		return true
	}
	
	return false
}

// OptionalAuth 可选认证中间件
func OptionalAuth(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := getSessionID(c)
		if sessionID != "" && isValidSession(sessionID, cfg) {
			c.Set("admin", true)
			c.Set("session_id", sessionID)
		}
		
		c.Next()
	}
}

// RateLimitByIP IP限流中间件
func RateLimitByIP() gin.HandlerFunc {
	// 这里可以实现基于IP的限流逻辑
	// 例如使用Redis存储IP访问次数
	return func(c *gin.Context) {
		// 获取客户端IP
		clientIP := c.ClientIP()
		
		// 这里添加限流逻辑
		// 例如：检查IP在时间窗口内的请求次数
		_ = clientIP
		
		c.Next()
	}
}

// AdminRequired 要求管理员权限
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		if admin, exists := c.Get("admin"); !exists || !admin.(bool) {
			c.JSON(http.StatusForbidden, model.ErrorResponse(403, "需要管理员权限"))
			c.Abort()
			return
		}
		
		c.Next()
	}
}
