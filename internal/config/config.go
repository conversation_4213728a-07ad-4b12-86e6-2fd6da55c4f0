package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Server      ServerConfig      `yaml:"server"`
	Database    DatabaseConfig    `yaml:"database"`
	Admin       AdminConfig       `yaml:"admin"`
	Upload      UploadConfig      `yaml:"upload"`
	Session     SessionConfig     `yaml:"session"`
	Security    SecurityConfig    `yaml:"security"`
	Log         LogConfig         `yaml:"log"`
	Pagination  PaginationConfig  `yaml:"pagination"`
	Cache       CacheConfig       `yaml:"cache"`
	Site        SiteConfig        `yaml:"site"`
	Development DevelopmentConfig `yaml:"development"`
	Production  ProductionConfig  `yaml:"production"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int    `yaml:"port"`
	Mode string `yaml:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver string       `yaml:"driver"`
	SQLite SQLiteConfig `yaml:"sqlite"`
	MySQL  MySQLConfig  `yaml:"mysql"`
}

// SQLiteConfig SQLite配置
type SQLiteConfig struct {
	Path string `yaml:"path"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
}

// AdminConfig 管理员配置
type AdminConfig struct {
	Password string `yaml:"password"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	MaxSize      int64    `yaml:"max_size"`
	AllowedTypes []string `yaml:"allowed_types"`
	StoragePath  string   `yaml:"storage_path"`
	URLPrefix    string   `yaml:"url_prefix"`
}

// SessionConfig 会话配置
type SessionConfig struct {
	Secret      string `yaml:"secret"`
	ExpireHours int    `yaml:"expire_hours"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	PasswordMinLength  int `yaml:"password_min_length"`
	MaxLoginAttempts   int `yaml:"max_login_attempts"`
	LockoutMinutes     int `yaml:"lockout_minutes"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `yaml:"level"`
	FilePath string `yaml:"file_path"`
}

// PaginationConfig 分页配置
type PaginationConfig struct {
	PageSize int `yaml:"page_size"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled bool        `yaml:"enabled"`
	Redis   RedisConfig `yaml:"redis"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// SiteConfig 网站信息配置
type SiteConfig struct {
	Title       string `yaml:"title"`
	Description string `yaml:"description"`
	Keywords    string `yaml:"keywords"`
}

// DevelopmentConfig 开发环境配置
type DevelopmentConfig struct {
	ShowErrors bool `yaml:"show_errors"`
	HotReload  bool `yaml:"hot_reload"`
}

// ProductionConfig 生产环境配置
type ProductionConfig struct {
	EnableGzip        bool `yaml:"enable_gzip"`
	StaticCacheTime   int  `yaml:"static_cache_time"`
	HideServerHeader  bool `yaml:"hide_server_header"`
}

var globalConfig *Config

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 从环境变量覆盖配置
	overrideFromEnv(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	globalConfig = &config
	return &config, nil
}

// Get 获取全局配置
func Get() *Config {
	return globalConfig
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(config *Config) {
	// 服务器配置
	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Server.Port = p
		}
	}
	if mode := os.Getenv("GIN_MODE"); mode != "" {
		config.Server.Mode = mode
	}

	// 数据库配置
	if driver := os.Getenv("DB_DRIVER"); driver != "" {
		config.Database.Driver = driver
	}
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Database.MySQL.Host = host
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Database.MySQL.Port = p
		}
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.Database.MySQL.Username = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Database.MySQL.Password = password
	}
	if database := os.Getenv("DB_NAME"); database != "" {
		config.Database.MySQL.Database = database
	}

	// 管理员密码
	if adminPassword := os.Getenv("ADMIN_PASSWORD"); adminPassword != "" {
		config.Admin.Password = adminPassword
	}

	// 会话密钥
	if sessionSecret := os.Getenv("SESSION_SECRET"); sessionSecret != "" {
		config.Session.Secret = sessionSecret
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证服务器端口
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", config.Server.Port)
	}

	// 验证服务器模式
	if config.Server.Mode != "debug" && config.Server.Mode != "release" && config.Server.Mode != "test" {
		return fmt.Errorf("无效的服务器模式: %s", config.Server.Mode)
	}

	// 验证数据库驱动
	if config.Database.Driver != "sqlite" && config.Database.Driver != "mysql" {
		return fmt.Errorf("不支持的数据库驱动: %s", config.Database.Driver)
	}

	// 验证SQLite配置
	if config.Database.Driver == "sqlite" {
		if config.Database.SQLite.Path == "" {
			return fmt.Errorf("SQLite数据库路径不能为空")
		}
	}

	// 验证MySQL配置
	if config.Database.Driver == "mysql" {
		if config.Database.MySQL.Host == "" {
			return fmt.Errorf("MySQL主机地址不能为空")
		}
		if config.Database.MySQL.Port <= 0 || config.Database.MySQL.Port > 65535 {
			return fmt.Errorf("无效的MySQL端口: %d", config.Database.MySQL.Port)
		}
		if config.Database.MySQL.Username == "" {
			return fmt.Errorf("MySQL用户名不能为空")
		}
		if config.Database.MySQL.Database == "" {
			return fmt.Errorf("MySQL数据库名不能为空")
		}
	}

	// 验证管理员密码
	if config.Admin.Password == "" {
		return fmt.Errorf("管理员密码不能为空")
	}
	if len(config.Admin.Password) < config.Security.PasswordMinLength {
		return fmt.Errorf("管理员密码长度不能少于%d位", config.Security.PasswordMinLength)
	}

	// 验证会话配置
	if config.Session.Secret == "" {
		return fmt.Errorf("会话密钥不能为空")
	}
	if len(config.Session.Secret) < 16 {
		return fmt.Errorf("会话密钥长度不能少于16位")
	}
	if config.Session.ExpireHours <= 0 {
		return fmt.Errorf("会话过期时间必须大于0")
	}

	// 验证上传配置
	if config.Upload.MaxSize <= 0 {
		return fmt.Errorf("文件上传大小限制必须大于0")
	}
	if len(config.Upload.AllowedTypes) == 0 {
		return fmt.Errorf("允许的文件类型不能为空")
	}
	if config.Upload.StoragePath == "" {
		return fmt.Errorf("文件存储路径不能为空")
	}

	// 验证分页配置
	if config.Pagination.PageSize <= 0 {
		return fmt.Errorf("分页大小必须大于0")
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	switch c.Database.Driver {
	case "sqlite":
		return c.Database.SQLite.Path
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Database.MySQL.Username,
			c.Database.MySQL.Password,
			c.Database.MySQL.Host,
			c.Database.MySQL.Port,
			c.Database.MySQL.Database,
		)
	default:
		return ""
	}
}

// IsProduction 判断是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Server.Mode == "release"
}

// IsDevelopment 判断是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.Server.Mode == "debug"
}

// GetServerAddr 获取服务器监听地址
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf(":%d", c.Server.Port)
}

// IsAllowedFileType 检查文件类型是否允许
func (c *Config) IsAllowedFileType(fileType string) bool {
	fileType = strings.ToLower(fileType)
	for _, allowedType := range c.Upload.AllowedTypes {
		if strings.ToLower(allowedType) == fileType {
			return true
		}
	}
	return false
}
