package model

import (
	"time"
)

// Portfolio 作品模型
type Portfolio struct {
	ID          uint             `json:"id" gorm:"primaryKey"`
	Title       string           `json:"title" gorm:"size:200;not null;comment:作品标题"`
	Description string           `json:"description" gorm:"type:text;comment:作品描述"`
	CategoryID  *uint            `json:"category_id" gorm:"comment:分类ID（可选）"`
	CoverImage  string           `json:"cover_image" gorm:"size:500;not null;comment:封面图片路径"`
	SortOrder   int              `json:"sort_order" gorm:"default:0;comment:排序顺序"`
	Status      string           `json:"status" gorm:"size:20;default:published;comment:状态"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
	
	// 关联关系
	Category *Category        `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Images   []PortfolioImage `json:"images,omitempty" gorm:"foreignKey:PortfolioID"`
}

// TableName 指定表名
func (Portfolio) TableName() string {
	return "portfolios"
}

// PortfolioStatus 作品状态常量
const (
	PortfolioStatusDraft     = "draft"     // 草稿
	PortfolioStatusPublished = "published" // 已发布
)

// PortfolioCreateRequest 创建作品请求
type PortfolioCreateRequest struct {
	Title       string                    `json:"title" binding:"required,max=200" example:"网站设计作品"`
	Description string                    `json:"description" binding:"max=2000" example:"这是一个响应式网站设计作品"`
	CategoryID  *uint                     `json:"category_id" example:"1"`
	CoverImage  string                    `json:"cover_image" binding:"required" example:"/uploads/cover.jpg"`
	Status      string                    `json:"status" binding:"oneof=draft published" example:"published"`
	SortOrder   int                       `json:"sort_order" example:"1"`
	Images      []PortfolioImageRequest   `json:"images,omitempty"`
}

// PortfolioUpdateRequest 更新作品请求
type PortfolioUpdateRequest struct {
	Title       string                    `json:"title" binding:"required,max=200" example:"网站设计作品"`
	Description string                    `json:"description" binding:"max=2000" example:"这是一个响应式网站设计作品"`
	CategoryID  *uint                     `json:"category_id" example:"1"`
	CoverImage  string                    `json:"cover_image" binding:"required" example:"/uploads/cover.jpg"`
	Status      string                    `json:"status" binding:"oneof=draft published" example:"published"`
	SortOrder   int                       `json:"sort_order" example:"1"`
	Images      []PortfolioImageRequest   `json:"images,omitempty"`
}

// PortfolioImageRequest 作品图片请求
type PortfolioImageRequest struct {
	ImageURL   string `json:"image_url" binding:"required" example:"/uploads/img1.jpg"`
	ImageName  string `json:"image_name" binding:"max=255" example:"图片1"`
	SortOrder  int    `json:"sort_order" example:"1"`
}

// PortfolioResponse 作品响应
type PortfolioResponse struct {
	ID          uint                     `json:"id"`
	Title       string                   `json:"title"`
	Description string                   `json:"description"`
	CategoryID  *uint                    `json:"category_id"`
	Category    *CategoryResponse        `json:"category,omitempty"`
	CoverImage  string                   `json:"cover_image"`
	SortOrder   int                      `json:"sort_order"`
	Status      string                   `json:"status"`
	Images      []PortfolioImageResponse `json:"images,omitempty"`
	CreatedAt   time.Time                `json:"created_at"`
	UpdatedAt   time.Time                `json:"updated_at"`
}

// PortfolioListResponse 作品列表响应
type PortfolioListResponse struct {
	ID         uint              `json:"id"`
	Title      string            `json:"title"`
	CategoryID *uint             `json:"category_id"`
	Category   *CategoryResponse `json:"category,omitempty"`
	CoverImage string            `json:"cover_image"`
	SortOrder  int               `json:"sort_order"`
	Status     string            `json:"status"`
	CreatedAt  time.Time         `json:"created_at"`
}

// ToResponse 转换为详细响应格式
func (p *Portfolio) ToResponse() *PortfolioResponse {
	resp := &PortfolioResponse{
		ID:          p.ID,
		Title:       p.Title,
		Description: p.Description,
		CategoryID:  p.CategoryID,
		CoverImage:  p.CoverImage,
		SortOrder:   p.SortOrder,
		Status:      p.Status,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}
	
	// 转换分类信息
	if p.Category != nil {
		resp.Category = p.Category.ToResponse()
	}
	
	// 转换图片信息
	if len(p.Images) > 0 {
		resp.Images = make([]PortfolioImageResponse, len(p.Images))
		for i, img := range p.Images {
			resp.Images[i] = *img.ToResponse()
		}
	}
	
	return resp
}

// ToListResponse 转换为列表响应格式
func (p *Portfolio) ToListResponse() *PortfolioListResponse {
	resp := &PortfolioListResponse{
		ID:         p.ID,
		Title:      p.Title,
		CategoryID: p.CategoryID,
		CoverImage: p.CoverImage,
		SortOrder:  p.SortOrder,
		Status:     p.Status,
		CreatedAt:  p.CreatedAt,
	}
	
	// 转换分类信息
	if p.Category != nil {
		resp.Category = p.Category.ToResponse()
	}
	
	return resp
}

// IsPublished 判断是否已发布
func (p *Portfolio) IsPublished() bool {
	return p.Status == PortfolioStatusPublished
}

// IsDraft 判断是否为草稿
func (p *Portfolio) IsDraft() bool {
	return p.Status == PortfolioStatusDraft
}
