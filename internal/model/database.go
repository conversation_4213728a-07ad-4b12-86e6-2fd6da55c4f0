package model

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver string
	DSN    string
	Debug  bool
}

// InitDatabase 初始化数据库连接
func InitDatabase(config DatabaseConfig) error {
	var dialector gorm.Dialector
	
	switch config.Driver {
	case "sqlite":
		// 确保数据目录存在
		if err := os.MkdirAll("data", 0755); err != nil {
			return fmt.Errorf("创建数据目录失败: %w", err)
		}
		dialector = sqlite.Open(config.DSN)
	case "mysql":
		dialector = mysql.Open(config.DSN)
	default:
		return fmt.Errorf("不支持的数据库驱动: %s", config.Driver)
	}
	
	// 配置GORM日志
	var gormLogger logger.Interface
	if config.Debug {
		gormLogger = logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  logger.Info,
				IgnoreRecordNotFoundError: true,
				Colorful:                  true,
			},
		)
	} else {
		gormLogger = logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  logger.Warn,
				IgnoreRecordNotFoundError: true,
				Colorful:                  false,
			},
		)
	}
	
	// 连接数据库
	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	})
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	
	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库实例失败: %w", err)
	}
	
	// 设置连接池参数
	if config.Driver == "mysql" {
		sqlDB.SetMaxOpenConns(100)
		sqlDB.SetMaxIdleConns(10)
		sqlDB.SetConnMaxLifetime(time.Hour)
	} else {
		// SQLite 使用较小的连接池
		sqlDB.SetMaxOpenConns(1)
		sqlDB.SetMaxIdleConns(1)
		sqlDB.SetConnMaxLifetime(time.Hour)
	}
	
	DB = db
	return nil
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}
	
	// 迁移所有模型
	err := DB.AutoMigrate(
		&Category{},
		&Portfolio{},
		&PortfolioImage{},
	)
	if err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}
	
	// 创建索引
	if err := createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}
	
	// 初始化默认数据
	if err := seedDefaultData(); err != nil {
		return fmt.Errorf("初始化默认数据失败: %w", err)
	}
	
	return nil
}

// createIndexes 创建数据库索引
func createIndexes() error {
	// 为portfolios表创建索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_portfolios_category_status ON portfolios(category_id, status)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_portfolios_status_sort ON portfolios(status, sort_order)").Error; err != nil {
		return err
	}
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_portfolios_created_status ON portfolios(created_at DESC, status)").Error; err != nil {
		return err
	}
	
	// 为portfolio_images表创建索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_portfolio_images_portfolio_sort ON portfolio_images(portfolio_id, sort_order)").Error; err != nil {
		return err
	}
	
	// 为categories表创建索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order)").Error; err != nil {
		return err
	}
	
	return nil
}

// seedDefaultData 初始化默认数据
func seedDefaultData() error {
	// 检查是否已有分类数据
	var count int64
	if err := DB.Model(&Category{}).Count(&count).Error; err != nil {
		return err
	}
	
	// 如果没有分类数据，创建默认分类
	if count == 0 {
		defaultCategories := []Category{
			{Name: "网页设计", Description: "网页设计相关作品", SortOrder: 1},
			{Name: "移动应用", Description: "移动应用设计作品", SortOrder: 2},
			{Name: "平面设计", Description: "平面设计相关作品", SortOrder: 3},
			{Name: "UI/UX设计", Description: "UI/UX设计作品", SortOrder: 4},
			{Name: "插画设计", Description: "插画和绘画作品", SortOrder: 5},
		}
		
		for _, category := range defaultCategories {
			if err := DB.Create(&category).Error; err != nil {
				return fmt.Errorf("创建默认分类失败: %w", err)
			}
		}
		
		log.Println("已创建默认分类数据")
	}
	
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if DB == nil {
		return nil
	}
	
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	
	return sqlDB.Close()
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}
