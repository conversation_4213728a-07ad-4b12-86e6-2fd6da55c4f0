{{template "layout.html" .}}

{{define "content"}}
<div class="row g-4">
    <!-- 统计卡片 -->
    <div class="col-xl-3 col-md-6">
        <div class="card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">总作品数</h6>
                        <h2 class="mb-0" id="totalPortfolios">
                            <div class="spinner-border spinner-border-sm"></div>
                        </h2>
                    </div>
                    <div class="opacity-75">
                        <i class="bi bi-collection" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">已发布</h6>
                        <h2 class="mb-0" id="publishedPortfolios">
                            <div class="spinner-border spinner-border-sm"></div>
                        </h2>
                    </div>
                    <div class="opacity-75">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">分类数量</h6>
                        <h2 class="mb-0" id="totalCategories">
                            <div class="spinner-border spinner-border-sm"></div>
                        </h2>
                    </div>
                    <div class="opacity-75">
                        <i class="bi bi-tags" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">草稿作品</h6>
                        <h2 class="mb-0" id="draftPortfolios">
                            <div class="spinner-border spinner-border-sm"></div>
                        </h2>
                    </div>
                    <div class="opacity-75">
                        <i class="bi bi-file-earmark" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mt-2">
    <!-- 最近作品 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>最近作品
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>作品</th>
                                <th>分类</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="recentPortfoliosTable">
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="spinner-border"></div>
                                    <div class="mt-2">加载中...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="/admin/portfolios/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>添加新作品
                    </a>
                    <a href="/admin/categories" class="btn btn-outline-primary">
                        <i class="bi bi-tags me-2"></i>管理分类
                    </a>
                    <a href="/" target="_blank" class="btn btn-outline-secondary">
                        <i class="bi bi-eye me-2"></i>查看前台
                    </a>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="card mt-4">
            <div class="card-header bg-white">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>系统信息
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span>版本：</span>
                        <span class="fw-bold">v1.0.0</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>数据库：</span>
                        <span class="fw-bold">SQLite</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>状态：</span>
                        <span class="badge bg-success">运行中</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

async function loadDashboardData() {
    try {
        // 加载统计数据
        const [portfoliosRes, categoriesRes] = await Promise.all([
            axios.get('/api/admin/portfolios'),
            axios.get('/api/admin/categories')
        ]);

        const portfolios = portfoliosRes.data.data || [];
        const categories = categoriesRes.data.data || [];

        // 更新统计数字
        document.getElementById('totalPortfolios').textContent = portfolios.length;
        document.getElementById('publishedPortfolios').textContent = 
            portfolios.filter(p => p.status === 'published').length;
        document.getElementById('draftPortfolios').textContent = 
            portfolios.filter(p => p.status === 'draft').length;
        document.getElementById('totalCategories').textContent = categories.length;

        // 加载最近作品
        loadRecentPortfolios(portfolios.slice(0, 5));

    } catch (error) {
        console.error('加载仪表盘数据失败:', error);
        showError('加载数据失败');
    }
}

function loadRecentPortfolios(portfolios) {
    const tbody = document.querySelector('#recentPortfoliosTable');
    
    if (portfolios.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted py-4">暂无作品</td></tr>';
        return;
    }

    tbody.innerHTML = portfolios.map(portfolio => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${portfolio.cover_image}" class="rounded me-3" width="50" height="50" style="object-fit: cover;">
                    <div>
                        <div class="fw-semibold">${portfolio.title}</div>
                        <small class="text-muted">${truncateText(portfolio.description || '', 30)}</small>
                    </div>
                </div>
            </td>
            <td>
                ${portfolio.category ? `<span class="badge bg-light text-dark">${portfolio.category.name}</span>` : '<span class="text-muted">无分类</span>'}
            </td>
            <td>
                <span class="badge ${portfolio.status === 'published' ? 'bg-success' : 'bg-secondary'}">
                    ${portfolio.status === 'published' ? '已发布' : '草稿'}
                </span>
            </td>
            <td>
                <small>${formatDate(portfolio.created_at)}</small>
            </td>
            <td>
                <a href="/admin/portfolios/${portfolio.id}/edit" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-pencil"></i>
                </a>
            </td>
        </tr>
    `).join('');
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}
</script>
{{end}}
