package middleware

import (
	"fmt"
	"log"
	"net/http"
	"portfolio-website/internal/model"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic信息
				log.Printf("PANIC: %v\n%s", err, debug.Stack())
				
				// 返回500错误
				if !c.Writer.Written() {
					c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "服务器内部错误"))
				}
				c.Abort()
			}
		}()
		
		c.Next()
		
		// 处理请求过程中的错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			
			// 记录错误
			log.Printf("ERROR: %s", err.Error())
			
			// 如果还没有写入响应，返回错误信息
			if !c.Writer.Written() {
				// 根据错误类型返回不同的状态码
				statusCode := getStatusCodeFromError(err.Error())
				c.<PERSON>(statusCode, model.ErrorResponse(statusCode, err.Error()))
			}
		}
	}
}

// getStatusCodeFromError 根据错误信息获取状态码
func getStatusCodeFromError(errMsg string) int {
	switch {
	case contains(errMsg, "不存在", "未找到", "not found"):
		return http.StatusNotFound
	case contains(errMsg, "无权限", "权限不足", "unauthorized", "forbidden"):
		return http.StatusForbidden
	case contains(errMsg, "参数错误", "无效", "格式错误", "bad request"):
		return http.StatusBadRequest
	case contains(errMsg, "未认证", "需要登录", "unauthenticated"):
		return http.StatusUnauthorized
	case contains(errMsg, "冲突", "已存在", "conflict"):
		return http.StatusConflict
	case contains(errMsg, "请求过于频繁", "rate limit"):
		return http.StatusTooManyRequests
	default:
		return http.StatusInternalServerError
	}
}

// contains 检查字符串是否包含任一关键词
func contains(str string, keywords ...string) bool {
	for _, keyword := range keywords {
		if len(str) >= len(keyword) {
			for i := 0; i <= len(str)-len(keyword); i++ {
				if str[i:i+len(keyword)] == keyword {
					return true
				}
			}
		}
	}
	return false
}

// CustomRecovery 自定义恢复中间件
func CustomRecovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.Printf("PANIC recovered: %s\n%s", err, debug.Stack())
			c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "服务器内部错误"))
		} else {
			log.Printf("PANIC recovered: %v\n%s", recovered, debug.Stack())
			c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "服务器内部错误"))
		}
		c.Abort()
	})
}

// ValidationErrorHandler 验证错误处理中间件
func ValidationErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		
		// 检查是否有验证错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			
			// 如果是验证错误，返回详细的错误信息
			if err.Type == gin.ErrorTypeBind {
				if !c.Writer.Written() {
					c.JSON(http.StatusBadRequest, model.ErrorResponse(400, fmt.Sprintf("参数验证失败: %s", err.Error())))
				}
				return
			}
		}
	}
}

// NotFoundHandler 404处理中间件
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusNotFound, model.ErrorResponse(404, "请求的资源不存在"))
	}
}

// MethodNotAllowedHandler 405处理中间件
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, model.ErrorResponse(405, "请求方法不被允许"))
	}
}

// TimeoutHandler 超时处理中间件
func TimeoutHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以添加超时处理逻辑
		c.Next()
	}
}

// DatabaseErrorHandler 数据库错误处理
func DatabaseErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			errMsg := err.Error()
			
			// 处理常见的数据库错误
			if contains(errMsg, "duplicate", "重复", "唯一约束") {
				if !c.Writer.Written() {
					c.JSON(http.StatusConflict, model.ErrorResponse(409, "数据已存在"))
				}
				return
			}
			
			if contains(errMsg, "foreign key", "外键约束") {
				if !c.Writer.Written() {
					c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "关联数据不存在"))
				}
				return
			}
			
			if contains(errMsg, "connection", "连接") {
				if !c.Writer.Written() {
					c.JSON(http.StatusServiceUnavailable, model.ErrorResponse(503, "数据库连接失败"))
				}
				return
			}
		}
	}
}
