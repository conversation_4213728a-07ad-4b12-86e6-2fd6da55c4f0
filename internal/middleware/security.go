package middleware

import (
	"fmt"

	"github.com/gin-gonic/gin"
)

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// X-Frame-Options: 防止点击劫持
		c.<PERSON>er("X-Frame-Options", "DENY")
		
		// X-Content-Type-Options: 防止MIME类型嗅探
		c.<PERSON><PERSON>("X-Content-Type-Options", "nosniff")
		
		// X-XSS-Protection: XSS保护
		c.Header("X-XSS-Protection", "1; mode=block")
		
		// Strict-Transport-Security: 强制HTTPS
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		
		// Referrer-Policy: 控制引用信息
		c.Head<PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")
		
		// Content-Security-Policy: 内容安全策略
		csp := "default-src 'self'; " +
			"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; " +
			"style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; " +
			"font-src 'self' https://fonts.gstatic.com; " +
			"img-src 'self' data: https:; " +
			"connect-src 'self'; " +
			"frame-ancestors 'none';"
		c.Header("Content-Security-Policy", csp)
		
		// Permissions-Policy: 权限策略
		c.Header("Permissions-Policy", "camera=(), microphone=(), geolocation=()")
		
		c.Next()
	}
}

// HideServerInfo 隐藏服务器信息中间件
func HideServerInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 移除或修改Server头
		c.Header("Server", "")
		
		c.Next()
	}
}

// RateLimitHeaders 速率限制头中间件
func RateLimitHeaders(limit int, window int) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-RateLimit-Limit", string(rune(limit)))
		c.Header("X-RateLimit-Window", string(rune(window)))
		
		c.Next()
	}
}

// NoCache 禁用缓存中间件
func NoCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		
		c.Next()
	}
}

// CacheControl 缓存控制中间件
func CacheControl(maxAge int) gin.HandlerFunc {
	return func(c *gin.Context) {
		if maxAge > 0 {
			c.Header("Cache-Control", fmt.Sprintf("public, max-age=%d", maxAge))
		} else {
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}
		
		c.Next()
	}
}
