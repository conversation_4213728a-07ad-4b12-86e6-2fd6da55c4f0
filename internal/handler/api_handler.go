package handler

import (
	"net/http"
	"portfolio-website/internal/model"
	"portfolio-website/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// APIHandler API处理器
type APIHandler struct {
	portfolioService service.PortfolioService
	categoryService  service.CategoryService
}

// NewAPIHandler 创建API处理器
func NewAPIHandler(portfolioService service.PortfolioService, categoryService service.CategoryService) *APIHandler {
	return &APIHandler{
		portfolioService: portfolioService,
		categoryService:  categoryService,
	}
}

// GetPortfolios 获取作品列表
// @Summary 获取作品列表
// @Description 获取已发布的作品列表，支持分页和分类筛选
// @Tags 前台API
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param per_page query int false "每页数量" default(12)
// @Param category_id query int false "分类ID"
// @Success 200 {object} model.PagedResponse
// @Router /api/portfolios [get]
func (h *APIHandler) GetPortfolios(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "12"))
	categoryID, _ := strconv.ParseUint(c.Query("category_id"), 10, 32)
	
	// 获取作品列表
	portfolios, total, err := h.portfolioService.GetPublishedPortfolios(page, perPage, uint(categoryID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取作品列表失败"))
		return
	}
	
	// 转换为响应格式
	var responses []*model.PortfolioListResponse
	for _, portfolio := range portfolios {
		responses = append(responses, portfolio.ToListResponse())
	}
	
	c.JSON(http.StatusOK, model.PagedSuccessResponse(responses, page, perPage, total))
}

// GetPortfolioByID 获取作品详情
// @Summary 获取作品详情
// @Description 根据ID获取单个作品的详细信息
// @Tags 前台API
// @Accept json
// @Produce json
// @Param id path int true "作品ID"
// @Success 200 {object} model.Response{data=model.PortfolioResponse}
// @Router /api/portfolios/{id} [get]
func (h *APIHandler) GetPortfolioByID(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的作品ID"))
		return
	}
	
	// 获取作品详情
	portfolio, err := h.portfolioService.GetPortfolioByID(uint(id))
	if err != nil {
		if err.Error() == "作品不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "作品不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取作品详情失败"))
		return
	}
	
	// 检查作品是否已发布
	if !portfolio.IsPublished() {
		c.JSON(http.StatusNotFound, model.ErrorResponse(404, "作品不存在"))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(portfolio.ToResponse()))
}

// GetCategories 获取分类列表
// @Summary 获取分类列表
// @Description 获取所有分类列表
// @Tags 前台API
// @Accept json
// @Produce json
// @Success 200 {object} model.Response{data=[]model.CategoryResponse}
// @Router /api/categories [get]
func (h *APIHandler) GetCategories(c *gin.Context) {
	// 获取分类列表
	categories, err := h.categoryService.GetCategories()
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取分类列表失败"))
		return
	}
	
	// 转换为响应格式
	var responses []*model.CategoryResponse
	for _, category := range categories {
		responses = append(responses, category.ToResponse())
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(responses))
}

// GetPortfoliosByCategory 获取分类下的作品
// @Summary 获取分类下的作品
// @Description 根据分类ID获取该分类下的所有已发布作品
// @Tags 前台API
// @Accept json
// @Produce json
// @Param id path int true "分类ID"
// @Param page query int false "页码" default(1)
// @Param per_page query int false "每页数量" default(12)
// @Success 200 {object} model.PagedResponse
// @Router /api/categories/{id}/portfolios [get]
func (h *APIHandler) GetPortfoliosByCategory(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	categoryID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的分类ID"))
		return
	}
	
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "12"))
	
	// 获取分类下的作品
	portfolios, total, err := h.portfolioService.GetPortfoliosByCategory(uint(categoryID), page, perPage)
	if err != nil {
		if err.Error() == "分类不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "分类不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取作品列表失败"))
		return
	}
	
	// 转换为响应格式
	var responses []*model.PortfolioListResponse
	for _, portfolio := range portfolios {
		responses = append(responses, portfolio.ToListResponse())
	}
	
	c.JSON(http.StatusOK, model.PagedSuccessResponse(responses, page, perPage, total))
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查服务状态
// @Tags 系统
// @Accept json
// @Produce json
// @Success 200 {object} model.Response{data=model.HealthResponse}
// @Router /api/health [get]
func (h *APIHandler) HealthCheck(c *gin.Context) {
	response := &model.HealthResponse{
		Status:    "ok",
		Timestamp: "2024-01-01T00:00:00Z",
		Version:   "1.0.0",
	}
	c.JSON(http.StatusOK, model.SuccessResponse(response))
}
