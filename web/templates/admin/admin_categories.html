{{template "admin_layout.html" .}}

{{define "content"}}
<!-- 操作栏 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0">分类管理</h4>
        <small class="text-muted">管理作品分类</small>
    </div>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal" onclick="showCreateModal()">
            <i class="bi bi-plus-circle me-2"></i>添加分类
        </button>
    </div>
</div>

<!-- 分类列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>分类名称</th>
                        <th>描述</th>
                        <th width="100">排序</th>
                        <th width="100">作品数量</th>
                        <th width="120">创建时间</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody id="categoriesTableBody">
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分类模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">添加分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId">
                    
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="categoryName" name="name" required
                               placeholder="请输入分类名称">
                    </div>

                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">分类描述</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"
                                  placeholder="请输入分类描述"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="categorySortOrder" class="form-label">排序顺序</label>
                        <input type="number" class="form-control" id="categorySortOrder" name="sort_order" min="0"
                               placeholder="数字越小排序越靠前" value="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveCategoryBtn" onclick="saveCategory()">
                    <span class="btn-text">保存</span>
                    <span class="btn-loading d-none">
                        <span class="spinner-border spinner-border-sm me-2"></span>
                        保存中...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除分类 "<span id="deleteCategoryName"></span>" 吗？</p>
                <p class="text-danger small">注意：如果该分类下有作品，将无法删除！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteCategoryBtn">删除</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
let categories = [];

document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
});

async function loadCategories() {
    try {
        const response = await axios.get('/api/admin/categories');
        categories = response.data.data || [];
        renderCategories();
    } catch (error) {
        console.error('加载分类失败:', error);
        showError('加载分类失败');
    }
}

function renderCategories() {
    const tbody = document.getElementById('categoriesTableBody');
    
    if (categories.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted py-4">暂无分类</td></tr>';
        return;
    }

    tbody.innerHTML = categories.map(category => `
        <tr>
            <td>
                <strong>${category.name}</strong>
            </td>
            <td>
                ${category.description || '<span class="text-muted">无描述</span>'}
            </td>
            <td>
                <span class="badge bg-light text-dark">${category.sort_order}</span>
            </td>
            <td>
                <span class="badge bg-primary">${category.portfolio_count || 0}</span>
            </td>
            <td>
                <small>${formatDate(category.created_at)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="showEditModal(${category.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="showDeleteModal(${category.id}, '${category.name}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function showCreateModal() {
    document.getElementById('categoryModalTitle').textContent = '添加分类';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('categorySortOrder').value = '0';
}

function showEditModal(categoryId) {
    const category = categories.find(c => c.id === categoryId);
    if (!category) return;

    document.getElementById('categoryModalTitle').textContent = '编辑分类';
    document.getElementById('categoryId').value = category.id;
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categoryDescription').value = category.description || '';
    document.getElementById('categorySortOrder').value = category.sort_order;
    
    new bootstrap.Modal(document.getElementById('categoryModal')).show();
}

async function saveCategory() {
    const form = document.getElementById('categoryForm');
    const formData = new FormData(form);
    const categoryId = document.getElementById('categoryId').value;
    
    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        sort_order: parseInt(formData.get('sort_order')) || 0
    };
    
    // 验证
    if (!data.name.trim()) {
        showError('请输入分类名称');
        return;
    }
    
    setLoading(true);
    
    try {
        let response;
        if (categoryId) {
            // 更新分类
            response = await axios.put(`/api/admin/categories/${categoryId}`, data);
        } else {
            // 创建分类
            response = await axios.post('/api/admin/categories', data);
        }
        
        if (response.data.code === 200) {
            showSuccess(categoryId ? '分类更新成功' : '分类创建成功');
            bootstrap.Modal.getInstance(document.getElementById('categoryModal')).hide();
            loadCategories(); // 重新加载数据
        }
    } catch (error) {
        console.error('保存分类失败:', error);
        if (error.response && error.response.data) {
            showError(error.response.data.message || '保存失败');
        } else {
            showError('保存失败，请稍后重试');
        }
    } finally {
        setLoading(false);
    }
}

function showDeleteModal(categoryId, categoryName) {
    document.getElementById('deleteCategoryName').textContent = categoryName;
    document.getElementById('confirmDeleteCategoryBtn').onclick = () => deleteCategory(categoryId);
    new bootstrap.Modal(document.getElementById('deleteCategoryModal')).show();
}

async function deleteCategory(categoryId) {
    try {
        await axios.delete(`/api/admin/categories/${categoryId}`);
        showSuccess('分类删除成功');
        bootstrap.Modal.getInstance(document.getElementById('deleteCategoryModal')).hide();
        loadCategories(); // 重新加载数据
    } catch (error) {
        console.error('删除分类失败:', error);
        if (error.response && error.response.data) {
            showError(error.response.data.message || '删除失败');
        } else {
            showError('删除失败，请稍后重试');
        }
    }
}

function setLoading(loading) {
    const saveBtn = document.getElementById('saveCategoryBtn');
    const btnText = saveBtn.querySelector('.btn-text');
    const btnLoading = saveBtn.querySelector('.btn-loading');
    
    if (loading) {
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        saveBtn.disabled = true;
    } else {
        btnText.classList.remove('d-none');
        btnLoading.classList.add('d-none');
        saveBtn.disabled = false;
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}
</script>
{{end}}
