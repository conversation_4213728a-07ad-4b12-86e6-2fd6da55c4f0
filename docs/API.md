# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    }
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "错误信息",
    "data": null
}
```

### 分页响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [],
        "total": 100,
        "page": 1,
        "page_size": 10,
        "total_pages": 10
    }
}
```

## 前台展示接口

### 1. 获取作品列表

**接口地址**: `GET /portfolios`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10 |
| status | string | 否 | 状态筛选：published |

**请求示例**:
```bash
GET /api/v1/portfolios?page=1&page_size=10&status=published
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "id": 1,
                "title": "网站设计作品",
                "description": "这是一个现代化的网站设计作品",
                "cover_image": "/uploads/cover1.jpg",
                "tags": "网页设计,UI设计,响应式",
                "view_count": 156,
                "created_at": "2024-01-15T10:30:00Z"
            }
        ],
        "total": 25,
        "page": 1,
        "page_size": 10,
        "total_pages": 3
    }
}
```

### 2. 获取作品详情

**接口地址**: `GET /portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

**请求示例**:
```bash
GET /api/v1/portfolios/1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "title": "网站设计作品",
        "description": "这是一个现代化的网站设计作品",
        "content": "详细的作品介绍内容...",
        "cover_image": "/uploads/cover1.jpg",
        "images": [
            "/uploads/image1.jpg",
            "/uploads/image2.jpg",
            "/uploads/image3.jpg"
        ],
        "tags": "网页设计,UI设计,响应式",
        "view_count": 157,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }
}
```



### 3. 获取推荐作品

**接口地址**: `GET /portfolios/featured`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | int | 否 | 数量限制，默认6 |

**请求示例**:
```bash
GET /api/v1/portfolios/featured?limit=6
```

## 认证接口

### 1. 管理员登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
    "username": "admin",
    "password": "password123"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin"
        },
        "expires_at": "2024-01-16T10:30:00Z"
    }
}
```

### 2. 获取用户信息

**接口地址**: `GET /auth/profile`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "avatar": "/uploads/avatar.jpg",
        "role": "admin",
        "created_at": "2024-01-01T00:00:00Z"
    }
}
```

### 3. 登出

**接口地址**: `POST /auth/logout`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

## 后台管理接口

> 以下接口均需要管理员权限，请求头需要包含有效的JWT Token

### 1. 作品管理

#### 1.1 获取作品列表

**接口地址**: `GET /admin/portfolios`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10 |
| status | string | 否 | 状态筛选：draft/published |

**请求头**:
```
Authorization: Bearer {token}
```

#### 1.2 创建作品

**接口地址**: `POST /admin/portfolios`

**请求参数**:
```json
{
    "title": "新作品标题",
    "description": "作品简介",
    "content": "详细内容",
    "cover_image": "/uploads/cover.jpg",
    "images": [
        "/uploads/image1.jpg",
        "/uploads/image2.jpg"
    ],
    "tags": "标签1,标签2,标签3",
    "status": "published",
    "sort_order": 0
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "作品创建成功",
    "data": {
        "id": 10,
        "title": "新作品标题",
        "description": "作品简介",
        "cover_image": "/uploads/cover.jpg",
        "status": "published",
        "created_at": "2024-01-15T10:30:00Z"
    }
}
```

#### 1.3 获取作品详情

**接口地址**: `GET /admin/portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

#### 1.4 更新作品

**接口地址**: `PUT /admin/portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

**请求参数**:
```json
{
    "title": "更新后的标题",
    "description": "更新后的简介",
    "status": "published"
}
```

#### 1.5 删除作品

**接口地址**: `DELETE /admin/portfolios/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 作品ID |

**响应示例**:
```json
{
    "code": 200,
    "message": "作品删除成功",
    "data": null
}
```



## 文件上传接口

### 1. 图片上传

#### 1.1 单图片上传

**接口地址**: `POST /uploads`

**请求方式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | 图片文件 |
| type | string | 否 | 文件类型：image(默认) |

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/api/v1/uploads \
  -H 'Authorization: Bearer {token}' \
  -F 'file=@/path/to/image.jpg' \
  -F 'type=image'
```

**响应示例**:
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "id": 1,
        "url": "/uploads/2024/01/15/image_123456.jpg",
        "filename": "image_123456.jpg",
        "original_name": "image.jpg",
        "size": 1024000,
        "mime_type": "image/jpeg",
        "created_at": "2024-01-15T10:30:00Z"
    }
}
```

#### 1.2 批量图片上传

**接口地址**: `POST /uploads/batch`

**请求方式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| files | file[] | 是 | 多个图片文件 |

**响应示例**:
```json
{
    "code": 200,
    "message": "批量上传成功",
    "data": {
        "success": [
            {
                "id": 1,
                "url": "/uploads/2024/01/15/image1_123456.jpg",
                "filename": "image1_123456.jpg",
                "original_name": "image1.jpg"
            },
            {
                "id": 2,
                "url": "/uploads/2024/01/15/image2_123457.jpg",
                "filename": "image2_123457.jpg",
                "original_name": "image2.jpg"
            }
        ],
        "failed": []
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 请求示例

### 使用curl
```bash
# 获取作品列表
curl -X GET "http://localhost:8080/api/v1/portfolios?page=1&page_size=10"

# 管理员登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# 创建作品（需要认证）
curl -X POST "http://localhost:8080/api/v1/admin/portfolios" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"title":"新作品","description":"作品描述","category_id":1}'

# 获取单个作品
curl -X GET "http://localhost:8080/api/v1/portfolios/1"

# 更新作品
curl -X PUT "http://localhost:8080/api/v1/admin/portfolios/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"title":"更新的作品标题"}'

# 删除作品
curl -X DELETE "http://localhost:8080/api/v1/admin/portfolios/1" \
  -H "Authorization: Bearer {token}"
```

### 使用JavaScript (Axios)
```javascript
// 设置默认请求头
axios.defaults.baseURL = 'http://localhost:8080/api/v1';
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

// 获取作品列表
const portfolios = await axios.get('/portfolios', {
    params: { page: 1, page_size: 10 }
});

// 获取单个作品
const portfolio = await axios.get('/portfolios/1');

// 创建作品
const newPortfolio = await axios.post('/admin/portfolios', {
    title: '新作品',
    description: '作品描述',
    category_id: 1
});

// 更新作品
const updatedPortfolio = await axios.put('/admin/portfolios/1', {
    title: '更新的作品标题'
});

// 删除作品
await axios.delete('/admin/portfolios/1');

// 获取分类列表
const categories = await axios.get('/categories');

// 创建分类
const newCategory = await axios.post('/admin/categories', {
    name: '新分类',
    description: '分类描述'
});
```
