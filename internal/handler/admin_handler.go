package handler

import (
	"net/http"
	"portfolio-website/internal/config"
	"portfolio-website/internal/model"
	"portfolio-website/internal/service"
	"portfolio-website/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminHandler 管理后台处理器
type AdminHandler struct {
	portfolioService service.PortfolioService
	categoryService  service.CategoryService
	config           *config.Config
}

// NewAdminHandler 创建管理后台处理器
func NewAdminHandler(portfolioService service.PortfolioService, categoryService service.CategoryService, cfg *config.Config) *AdminHandler {
	return &AdminHandler{
		portfolioService: portfolioService,
		categoryService:  categoryService,
		config:           cfg,
	}
}

// Login 管理员登录
func (h *AdminHandler) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, model.ErrorResponse(400, "参数错误"))
		return
	}

	// 验证密码
	if req.Password != h.config.Admin.Password {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "密码错误"))
		return
	}

	// 创建会话
	sessionService := service.GetSessionService()
	session, err := sessionService.CreateSession("admin", "admin", h.config.Session.ExpireHours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "创建会话失败"))
		return
	}

	// 设置Cookie
	c.SetCookie("session_id", session.ID, h.config.Session.ExpireHours*3600, "/", "", false, true)

	// 返回响应
	response := &model.LoginResponse{
		SessionID: session.ID,
		Message:   "登录成功",
	}

	c.JSON(http.StatusOK, model.SuccessResponse(response))
}

// GetPortfolios 获取作品列表（管理）
func (h *AdminHandler) GetPortfolios(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "10"))
	categoryID, _ := strconv.ParseUint(c.Query("category_id"), 10, 32)
	
	// 获取作品列表
	portfolios, total, err := h.portfolioService.GetPortfolios(page, perPage, uint(categoryID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取作品列表失败"))
		return
	}
	
	// 转换为响应格式
	var responses []*model.PortfolioResponse
	for _, portfolio := range portfolios {
		responses = append(responses, portfolio.ToResponse())
	}
	
	c.JSON(http.StatusOK, model.PagedSuccessResponse(responses, page, perPage, total))
}

// CreatePortfolio 创建作品
func (h *AdminHandler) CreatePortfolio(c *gin.Context) {
	var req model.PortfolioCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "参数错误"))
		return
	}
	
	// 创建作品
	portfolio, err := h.portfolioService.CreatePortfolio(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(portfolio.ToResponse()))
}

// GetPortfolioByID 获取作品详情（管理）
func (h *AdminHandler) GetPortfolioByID(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的作品ID"))
		return
	}
	
	// 获取作品详情
	portfolio, err := h.portfolioService.GetPortfolioByID(uint(id))
	if err != nil {
		if err.Error() == "作品不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "作品不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取作品详情失败"))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(portfolio.ToResponse()))
}

// UpdatePortfolio 更新作品
func (h *AdminHandler) UpdatePortfolio(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的作品ID"))
		return
	}
	
	var req model.PortfolioUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "参数错误"))
		return
	}
	
	// 更新作品
	portfolio, err := h.portfolioService.UpdatePortfolio(uint(id), &req)
	if err != nil {
		if err.Error() == "作品不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "作品不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(portfolio.ToResponse()))
}

// DeletePortfolio 删除作品
func (h *AdminHandler) DeletePortfolio(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的作品ID"))
		return
	}
	
	// 删除作品
	if err := h.portfolioService.DeletePortfolio(uint(id)); err != nil {
		if err.Error() == "作品不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "作品不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(gin.H{"message": "作品删除成功"}))
}

// GetCategories 获取分类列表（管理）
func (h *AdminHandler) GetCategories(c *gin.Context) {
	// 获取分类列表
	categories, err := h.categoryService.GetCategoriesWithCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取分类列表失败"))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(categories))
}

// CreateCategory 创建分类
func (h *AdminHandler) CreateCategory(c *gin.Context) {
	var req model.CategoryCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "参数错误"))
		return
	}
	
	// 创建分类
	category, err := h.categoryService.CreateCategory(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(category.ToResponse()))
}

// GetCategoryByID 获取分类详情（管理）
func (h *AdminHandler) GetCategoryByID(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的分类ID"))
		return
	}
	
	// 获取分类详情
	category, err := h.categoryService.GetCategoryByID(uint(id))
	if err != nil {
		if err.Error() == "分类不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "分类不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "获取分类详情失败"))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(category.ToResponse()))
}

// UpdateCategory 更新分类
func (h *AdminHandler) UpdateCategory(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的分类ID"))
		return
	}
	
	var req model.CategoryUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "参数错误"))
		return
	}
	
	// 更新分类
	category, err := h.categoryService.UpdateCategory(uint(id), &req)
	if err != nil {
		if err.Error() == "分类不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "分类不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(category.ToResponse()))
}

// DeleteCategory 删除分类
func (h *AdminHandler) DeleteCategory(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的分类ID"))
		return
	}
	
	// 删除分类
	if err := h.categoryService.DeleteCategory(uint(id)); err != nil {
		if err.Error() == "分类不存在" {
			c.JSON(http.StatusNotFound, model.ErrorResponse(404, "分类不存在"))
			return
		}
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}
	
	c.JSON(http.StatusOK, model.SuccessResponse(gin.H{"message": "分类删除成功"}))
}

// UploadFile 文件上传
func (h *AdminHandler) UploadFile(c *gin.Context) {
	// 获取上传的文件
	fileHeader, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "未找到上传文件"))
		return
	}

	// 验证文件
	if err := utils.ValidateFile(fileHeader, h.config); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, err.Error()))
		return
	}

	// 保存文件
	fileInfo, err := utils.SaveFile(fileHeader, h.config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, "保存文件失败: "+err.Error()))
		return
	}

	// 构建响应
	response := &model.UploadResponse{
		URL:      fileInfo.URL,
		Filename: fileInfo.SavedName,
		Size:     fileInfo.Size,
		Type:     fileInfo.Type,
	}

	c.JSON(http.StatusOK, model.SuccessResponse(response))
}
