# RESTful API 设计规范

## 1. RESTful 设计原则

本项目严格遵循RESTful API设计规范，确保API的一致性、可预测性和易用性。

### 1.1 资源导向设计

API设计以资源为中心，每个URL代表一个资源或资源集合：

```
GET    /api/v1/portfolios          # 作品集合
GET    /api/v1/portfolios/{id}     # 单个作品资源
POST   /api/v1/portfolios          # 创建作品资源
PUT    /api/v1/portfolios/{id}     # 更新作品资源
DELETE /api/v1/portfolios/{id}     # 删除作品资源
```

### 1.2 HTTP方法语义

| HTTP方法 | 语义 | 幂等性 | 安全性 |
|----------|------|--------|--------|
| GET | 获取资源 | ✅ | ✅ |
| POST | 创建资源 | ❌ | ❌ |
| PUT | 更新/替换资源 | ✅ | ❌ |
| PATCH | 部分更新资源 | ❌ | ❌ |
| DELETE | 删除资源 | ✅ | ❌ |

### 1.3 状态码使用规范

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | OK | 成功获取资源 |
| 201 | Created | 成功创建资源 |
| 204 | No Content | 成功删除资源或更新无返回内容 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证 |
| 403 | Forbidden | 无权限访问 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 422 | Unprocessable Entity | 数据验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

## 2. URL 设计规范

### 2.1 基本规则

- 使用名词而非动词
- 使用复数形式表示集合
- 使用小写字母
- 使用连字符分隔单词
- 避免深层嵌套（最多3层）

### 2.2 资源层次结构

```
/api/v1/portfolios                    # 作品集合
/api/v1/portfolios/{id}               # 单个作品
/api/v1/portfolios/{id}/images        # 作品的图片集合
/api/v1/portfolios/{id}/images/{id}   # 作品的单张图片

/api/v1/categories                    # 分类集合
/api/v1/categories/{id}               # 单个分类
/api/v1/categories/{id}/portfolios    # 分类下的作品集合
```

### 2.3 查询参数规范

```
# 分页参数
GET /api/v1/portfolios?page=1&page_size=10

# 筛选参数
GET /api/v1/portfolios?category_id=1&status=published

# 排序参数
GET /api/v1/portfolios?sort=created_at&order=desc

# 字段选择
GET /api/v1/portfolios?fields=id,title,cover_image
```

## 3. 请求和响应格式

### 3.1 请求格式

#### Content-Type
- JSON请求：`application/json`
- 文件上传：`multipart/form-data`
- 表单提交：`application/x-www-form-urlencoded`

#### 请求头
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
Accept: application/json
```

#### 请求体示例
```json
{
    "title": "作品标题",
    "description": "作品描述",
    "category_id": 1,
    "tags": ["设计", "网页", "UI"],
    "status": "published"
}
```

### 3.2 响应格式

#### 统一响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 分页响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [],
        "pagination": {
            "page": 1,
            "page_size": 10,
            "total": 100,
            "total_pages": 10,
            "has_next": true,
            "has_prev": false
        }
    }
}
```

#### 错误响应结构
```json
{
    "code": 400,
    "message": "请求参数错误",
    "errors": [
        {
            "field": "title",
            "message": "标题不能为空"
        }
    ],
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## 4. 具体API设计

### 4.1 作品资源 (Portfolios)

```http
# 获取作品列表
GET /api/v1/portfolios
Query Parameters:
- page: 页码 (默认: 1)
- page_size: 每页数量 (默认: 10, 最大: 100)
- category_id: 分类ID
- status: 状态 (published/draft)
- sort: 排序字段 (created_at/updated_at/view_count)
- order: 排序方向 (asc/desc)

# 获取单个作品
GET /api/v1/portfolios/{id}

# 获取推荐作品
GET /api/v1/portfolios/featured
Query Parameters:
- limit: 数量限制 (默认: 6, 最大: 20)

# 创建作品 (需要认证)
POST /api/v1/admin/portfolios
Body: {
    "title": "string",
    "description": "string",
    "content": "string",
    "cover_image": "string",
    "images": ["string"],
    "category_id": "integer",
    "tags": "string",
    "status": "string",
    "sort_order": "integer"
}

# 更新作品 (需要认证)
PUT /api/v1/admin/portfolios/{id}
Body: 同创建作品，所有字段可选

# 删除作品 (需要认证)
DELETE /api/v1/admin/portfolios/{id}
```

### 4.2 分类资源 (Categories)

```http
# 获取分类列表
GET /api/v1/categories

# 获取单个分类
GET /api/v1/categories/{id}

# 获取分类下的作品
GET /api/v1/categories/{id}/portfolios
Query Parameters: 同作品列表

# 创建分类 (需要认证)
POST /api/v1/admin/categories
Body: {
    "name": "string",
    "description": "string",
    "sort_order": "integer"
}

# 更新分类 (需要认证)
PUT /api/v1/admin/categories/{id}
Body: 同创建分类，所有字段可选

# 删除分类 (需要认证)
DELETE /api/v1/admin/categories/{id}
```

### 4.3 文件上传资源 (Uploads)

```http
# 单文件上传 (需要认证)
POST /api/v1/uploads
Content-Type: multipart/form-data
Body: {
    "file": "binary",
    "type": "string" // image/document/video
}

# 批量文件上传 (需要认证)
POST /api/v1/uploads/batch
Content-Type: multipart/form-data
Body: {
    "files": ["binary"],
    "type": "string"
}

# 获取文件信息
GET /api/v1/uploads/{id}

# 删除文件 (需要认证)
DELETE /api/v1/uploads/{id}
```

### 4.4 认证资源 (Auth)

```http
# 用户登录
POST /api/v1/auth/login
Body: {
    "username": "string",
    "password": "string"
}

# 用户登出 (需要认证)
POST /api/v1/auth/logout

# 获取用户信息 (需要认证)
GET /api/v1/auth/profile

# 刷新Token (需要认证)
POST /api/v1/auth/refresh
```

## 5. 版本控制

### 5.1 版本策略
- 使用URL路径版本控制：`/api/v1/`
- 主版本号变更时创建新的路径：`/api/v2/`
- 向后兼容的更改在同一版本内进行

### 5.2 版本演进
```
/api/v1/portfolios    # 当前版本
/api/v2/portfolios    # 未来版本（如有重大变更）
```

## 6. 错误处理

### 6.1 错误响应格式
```json
{
    "code": 400,
    "message": "请求参数错误",
    "errors": [
        {
            "field": "title",
            "code": "required",
            "message": "标题字段是必需的"
        },
        {
            "field": "category_id",
            "code": "invalid",
            "message": "分类ID无效"
        }
    ],
    "request_id": "req_123456789",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 6.2 常见错误场景
- 参数验证失败：422 Unprocessable Entity
- 资源不存在：404 Not Found
- 权限不足：403 Forbidden
- 认证失败：401 Unauthorized
- 资源冲突：409 Conflict

## 7. 性能优化

### 7.1 缓存策略
```http
# 响应头
Cache-Control: public, max-age=3600
ETag: "33a64df551425fcc55e4d42a148795d9f25f89d4"
Last-Modified: Wed, 15 Jan 2024 10:30:00 GMT

# 条件请求
If-None-Match: "33a64df551425fcc55e4d42a148795d9f25f89d4"
If-Modified-Since: Wed, 15 Jan 2024 10:30:00 GMT
```

### 7.2 分页优化
- 使用游标分页处理大数据集
- 提供总数统计的开关选项
- 支持字段选择减少数据传输

### 7.3 批量操作
```http
# 批量创建
POST /api/v1/admin/portfolios/batch
Body: {
    "items": [
        {"title": "作品1", "category_id": 1},
        {"title": "作品2", "category_id": 2}
    ]
}

# 批量更新
PATCH /api/v1/admin/portfolios/batch
Body: {
    "items": [
        {"id": 1, "status": "published"},
        {"id": 2, "status": "draft"}
    ]
}
```

这个RESTful设计规范确保了API的一致性、可维护性和易用性，为前后端开发提供了清晰的指导原则。
