/* 作品展示网站样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-size: 1.5rem;
    color: #2c3e50 !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #007bff !important;
}

/* 作品卡片样式 */
.portfolio-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
}

.portfolio-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.card-img-wrapper {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.card-img-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-card:hover .card-img-wrapper img {
    transform: scale(1.05);
}

.card-img-overlay {
    background: rgba(0, 0, 0, 0.7);
    transition: opacity 0.3s ease;
}

.card-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.card-text {
    font-size: 0.9rem;
    line-height: 1.5;
}

/* 作品详情页样式 */
.portfolio-image {
    max-height: 600px;
    object-fit: contain;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.portfolio-image:hover {
    transform: scale(1.02);
}

.portfolio-thumb {
    height: 60px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.portfolio-thumb:hover {
    transform: scale(1.1);
    border-color: #007bff !important;
}

.carousel-item img {
    height: 500px;
    object-fit: contain;
    background: #f8f9fa;
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: #007bff;
    border-color: #007bff;
    transform: translateY(-2px);
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 50%;
    margin: 0 2px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #dee2e6;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    border-top: 1px solid #dee2e6;
}

/* 空状态样式 */
.empty-state {
    padding: 4rem 0;
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
    
    .card-img-wrapper {
        height: 200px;
    }
    
    .carousel-item img {
        height: 300px;
    }
    
    .portfolio-image {
        max-height: 400px;
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-img-wrapper {
        height: 180px;
    }
    
    .carousel-item img {
        height: 250px;
    }
    
    .portfolio-thumb {
        height: 40px;
    }
    
    .btn {
        font-size: 0.875rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 图片懒加载效果 */
img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

/* 模态框样式 */
.modal-xl {
    max-width: 90vw;
}

.modal-body img {
    max-height: 80vh;
    object-fit: contain;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
