package middleware

import (
	"fmt"
	"io"
	"log"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

// CustomLogger 自定义日志中间件
func CustomLogger() gin.HandlerFunc {
	return gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: customLogFormatter,
		Output:    getLogWriter(),
	})
}

// customLogFormatter 自定义日志格式
func customLogFormatter(param gin.LogFormatterParams) string {
	return fmt.Sprintf("[%s] %s %s %d %s %s %s\n",
		param.TimeStamp.Format("2006-01-02 15:04:05"),
		param.ClientIP,
		param.Method,
		param.StatusCode,
		param.Latency,
		param.Path,
		param.ErrorMessage,
	)
}

// getLogWriter 获取日志写入器
func getLogWriter() io.Writer {
	// 确保日志目录存在
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Printf("创建日志目录失败: %v", err)
		return os.Stdout
	}
	
	// 打开日志文件
	logFile, err := os.OpenFile("logs/app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("打开日志文件失败: %v", err)
		return os.Stdout
	}
	
	// 同时输出到控制台和文件
	return io.MultiWriter(os.Stdout, logFile)
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		
		// 处理请求
		c.Next()
		
		// 计算延迟
		latency := time.Since(start)
		
		// 获取状态码
		statusCode := c.Writer.Status()
		
		// 获取客户端IP
		clientIP := c.ClientIP()
		
		// 获取请求方法
		method := c.Request.Method
		
		// 构建完整路径
		if raw != "" {
			path = path + "?" + raw
		}
		
		// 获取用户代理
		userAgent := c.Request.UserAgent()
		
		// 记录日志
		logMessage := fmt.Sprintf("[%s] %s %s %d %v \"%s\"",
			time.Now().Format("2006-01-02 15:04:05"),
			clientIP,
			method,
			statusCode,
			latency,
			path,
		)
		
		// 如果有错误，添加错误信息
		if len(c.Errors) > 0 {
			logMessage += fmt.Sprintf(" Error: %s", c.Errors.String())
		}
		
		// 添加用户代理（可选）
		if userAgent != "" {
			logMessage += fmt.Sprintf(" UserAgent: %s", userAgent)
		}
		
		// 根据状态码选择日志级别
		if statusCode >= 500 {
			log.Printf("ERROR %s", logMessage)
		} else if statusCode >= 400 {
			log.Printf("WARN %s", logMessage)
		} else {
			log.Printf("INFO %s", logMessage)
		}
	}
}

// AccessLogger 访问日志中间件
func AccessLogger(logFile string) gin.HandlerFunc {
	// 打开日志文件
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("打开访问日志文件失败: %v", err)
		return gin.Logger()
	}
	
	return gin.LoggerWithWriter(file)
}

// ErrorLogger 错误日志中间件
func ErrorLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		
		// 如果有错误，记录到错误日志
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				log.Printf("ERROR [%s] %s %s: %s",
					time.Now().Format("2006-01-02 15:04:05"),
					c.Request.Method,
					c.Request.URL.Path,
					err.Error(),
				)
			}
		}
	}
}
