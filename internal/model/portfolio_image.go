package model

import (
	"time"
)

// PortfolioImage 作品图片模型
type PortfolioImage struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	PortfolioID uint      `json:"portfolio_id" gorm:"not null;comment:作品ID"`
	ImageURL    string    `json:"image_url" gorm:"size:500;not null;comment:图片URL"`
	ImageName   string    `json:"image_name" gorm:"size:255;comment:图片名称"`
	SortOrder   int       `json:"sort_order" gorm:"default:0;comment:排序顺序"`
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联关系
	Portfolio *Portfolio `json:"portfolio,omitempty" gorm:"foreignKey:PortfolioID"`
}

// TableName 指定表名
func (PortfolioImage) TableName() string {
	return "portfolio_images"
}

// PortfolioImageResponse 作品图片响应
type PortfolioImageResponse struct {
	ID        uint      `json:"id"`
	ImageURL  string    `json:"image_url"`
	ImageName string    `json:"image_name"`
	SortOrder int       `json:"sort_order"`
	CreatedAt time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (pi *PortfolioImage) ToResponse() *PortfolioImageResponse {
	return &PortfolioImageResponse{
		ID:        pi.ID,
		ImageURL:  pi.ImageURL,
		ImageName: pi.ImageName,
		SortOrder: pi.SortOrder,
		CreatedAt: pi.CreatedAt,
	}
}
