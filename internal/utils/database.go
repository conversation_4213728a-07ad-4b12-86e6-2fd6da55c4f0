package utils

import (
	"fmt"
	"log"
	"portfolio-website/internal/config"
	"portfolio-website/internal/model"
)

// InitializeDatabase 初始化数据库
func InitializeDatabase(cfg *config.Config) error {
	// 构建数据库配置
	dbConfig := model.DatabaseConfig{
		Driver: cfg.Database.Driver,
		DSN:    cfg.GetDSN(),
		Debug:  cfg.IsDevelopment(),
	}
	
	// 初始化数据库连接
	if err := model.InitDatabase(dbConfig); err != nil {
		return fmt.Errorf("初始化数据库连接失败: %w", err)
	}
	
	log.Printf("数据库连接成功 (驱动: %s)", cfg.Database.Driver)
	
	// 自动迁移数据库表
	if err := model.AutoMigrate(); err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}
	
	log.Println("数据库迁移完成")
	
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	return model.CloseDatabase()
}

// TestDatabaseConnection 测试数据库连接
func TestDatabaseConnection() error {
	db := model.GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}
	
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库实例失败: %w", err)
	}
	
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}
	
	return nil
}
