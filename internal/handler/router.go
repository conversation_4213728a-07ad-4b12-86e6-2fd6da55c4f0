package handler

import (
	"html/template"
	"net/http"
	"portfolio-website/internal/config"
	"portfolio-website/internal/middleware"
	"portfolio-website/internal/service"

	"github.com/gin-gonic/gin"
)

// Router 路由管理器
type Router struct {
	engine           *gin.Engine
	config           *config.Config
	apiHandler       *APIHandler
	adminHandler     *AdminHandler
	webHandler       *WebHandler
	portfolioService service.PortfolioService
	categoryService  service.CategoryService
}

// NewRouter 创建路由管理器
func NewRouter(cfg *config.Config, portfolioService service.PortfolioService, categoryService service.CategoryService) *Router {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	engine := gin.New()

	// 设置HTML模板
	engine.SetFuncMap(template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"iterate": func(count int) []int {
			var items []int
			for i := 0; i < count; i++ {
				items = append(items, i)
			}
			return items
		},
		"slice": func(s string, start, end int) string {
			if start < 0 {
				start = 0
			}
			if end > len(s) {
				end = len(s)
			}
			if start >= end {
				return ""
			}
			return s[start:end]
		},
	})
	engine.LoadHTMLGlob("web/templates/**/*")

	// 创建处理器
	apiHandler := NewAPIHandler(portfolioService, categoryService)
	adminHandler := NewAdminHandler(portfolioService, categoryService, cfg)
	webHandler := NewWebHandler(portfolioService, categoryService, cfg)

	return &Router{
		engine:           engine,
		config:           cfg,
		apiHandler:       apiHandler,
		adminHandler:     adminHandler,
		webHandler:       webHandler,
		portfolioService: portfolioService,
		categoryService:  categoryService,
	}
}

// SetupRoutes 设置路由
func (r *Router) SetupRoutes() {
	// 添加全局中间件
	r.setupMiddleware()
	
	// 设置静态文件服务
	r.setupStaticFiles()
	
	// 设置API路由
	r.setupAPIRoutes()
	
	// 设置管理后台路由
	r.setupAdminRoutes()
	
	// 设置前台页面路由
	r.setupWebRoutes()
}

// setupMiddleware 设置中间件
func (r *Router) setupMiddleware() {
	// 恢复中间件
	r.engine.Use(gin.Recovery())
	
	// 日志中间件
	if r.config.IsDevelopment() {
		r.engine.Use(gin.Logger())
	} else {
		r.engine.Use(middleware.CustomLogger())
	}
	
	// CORS中间件
	r.engine.Use(middleware.CORS())
	
	// 安全头中间件
	if r.config.IsProduction() {
		r.engine.Use(middleware.SecurityHeaders())
		// 生产环境启用Gzip压缩
		if r.config.Production.EnableGzip {
			r.engine.Use(middleware.Gzip())
		}
	}
}

// setupStaticFiles 设置静态文件服务
func (r *Router) setupStaticFiles() {
	// 静态资源
	r.engine.Static("/static", "./web/static")
	
	// 上传文件
	r.engine.Static("/uploads", "./uploads")
	
	// Favicon
	r.engine.StaticFile("/favicon.ico", "./web/static/images/favicon.ico")
}

// setupAPIRoutes 设置API路由
func (r *Router) setupAPIRoutes() {
	api := r.engine.Group("/api")
	{
		// 健康检查
		api.GET("/health", r.apiHandler.HealthCheck)
		
		// 前台API - 作品相关
		api.GET("/portfolios", r.apiHandler.GetPortfolios)
		api.GET("/portfolios/:id", r.apiHandler.GetPortfolioByID)
		
		// 前台API - 分类相关
		api.GET("/categories", r.apiHandler.GetCategories)
		api.GET("/categories/:id/portfolios", r.apiHandler.GetPortfoliosByCategory)
	}
}

// setupAdminRoutes 设置管理后台路由
func (r *Router) setupAdminRoutes() {
	admin := r.engine.Group("/api/admin")
	{
		// 登录接口（不需要认证）
		admin.POST("/login", r.adminHandler.Login)
		
		// 需要认证的接口
		authenticated := admin.Group("")
		authenticated.Use(middleware.AdminAuth(r.config))
		{
			// 作品管理
			authenticated.GET("/portfolios", r.adminHandler.GetPortfolios)
			authenticated.POST("/portfolios", r.adminHandler.CreatePortfolio)
			authenticated.GET("/portfolios/:id", r.adminHandler.GetPortfolioByID)
			authenticated.PUT("/portfolios/:id", r.adminHandler.UpdatePortfolio)
			authenticated.DELETE("/portfolios/:id", r.adminHandler.DeletePortfolio)
			
			// 分类管理
			authenticated.GET("/categories", r.adminHandler.GetCategories)
			authenticated.POST("/categories", r.adminHandler.CreateCategory)
			authenticated.GET("/categories/:id", r.adminHandler.GetCategoryByID)
			authenticated.PUT("/categories/:id", r.adminHandler.UpdateCategory)
			authenticated.DELETE("/categories/:id", r.adminHandler.DeleteCategory)
			
			// 文件上传
			authenticated.POST("/upload", r.adminHandler.UploadFile)
		}
	}
}

// setupWebRoutes 设置前台页面路由
func (r *Router) setupWebRoutes() {
	// 前台页面
	r.engine.GET("/", r.webHandler.Index)
	r.engine.GET("/portfolio/:id", r.webHandler.PortfolioDetail)
	r.engine.GET("/category/:id", r.webHandler.CategoryPortfolios)
	
	// 管理后台页面
	admin := r.engine.Group("/admin")
	{
		admin.GET("/", r.webHandler.AdminIndex)
		admin.GET("/login", r.webHandler.AdminLogin)
		admin.GET("/portfolios", r.webHandler.AdminPortfolios)
		admin.GET("/portfolios/create", r.webHandler.AdminCreatePortfolio)
		admin.GET("/portfolios/:id/edit", r.webHandler.AdminEditPortfolio)
		admin.GET("/categories", r.webHandler.AdminCategories)
	}
}

// GetEngine 获取Gin引擎
func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}

// Run 启动服务器
func (r *Router) Run() error {
	return r.engine.Run(r.config.GetServerAddr())
}

// 404处理
func (r *Router) setupNotFoundHandler() {
	r.engine.NoRoute(func(c *gin.Context) {
		// 如果是API请求，返回JSON
		if c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "接口不存在",
			})
			return
		}
		
		// 否则返回404页面
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title": "页面不存在",
		})
	})
}
