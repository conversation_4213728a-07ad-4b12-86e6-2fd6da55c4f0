package model

import (
	"time"
)

// Category 分类模型
type Category struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"size:100;not null;comment:分类名称"`
	Description string    `json:"description" gorm:"type:text;comment:分类描述"`
	SortOrder   int       `json:"sort_order" gorm:"default:0;comment:排序顺序"`
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联关系
	Portfolios []Portfolio `json:"portfolios,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName 指定表名
func (Category) TableName() string {
	return "categories"
}

// CategoryCreateRequest 创建分类请求
type CategoryCreateRequest struct {
	Name        string `json:"name" binding:"required,max=100" example:"网页设计"`
	Description string `json:"description" binding:"max=500" example:"网页设计相关作品"`
	SortOrder   int    `json:"sort_order" example:"1"`
}

// CategoryUpdateRequest 更新分类请求
type CategoryUpdateRequest struct {
	Name        string `json:"name" binding:"required,max=100" example:"网页设计"`
	Description string `json:"description" binding:"max=500" example:"网页设计相关作品"`
	SortOrder   int    `json:"sort_order" example:"1"`
}

// CategoryResponse 分类响应
type CategoryResponse struct {
	ID            uint      `json:"id"`
	Name          string    `json:"name"`
	Description   string    `json:"description"`
	SortOrder     int       `json:"sort_order"`
	PortfolioCount int64    `json:"portfolio_count,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (c *Category) ToResponse() *CategoryResponse {
	return &CategoryResponse{
		ID:          c.ID,
		Name:        c.Name,
		Description: c.Description,
		SortOrder:   c.SortOrder,
		CreatedAt:   c.CreatedAt,
	}
}

// ToResponseWithCount 转换为带作品数量的响应格式
func (c *Category) ToResponseWithCount(count int64) *CategoryResponse {
	resp := c.ToResponse()
	resp.PortfolioCount = count
	return resp
}
