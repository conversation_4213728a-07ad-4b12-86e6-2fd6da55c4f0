package utils

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"portfolio-website/internal/config"
)

// FileInfo 文件信息
type FileInfo struct {
	OriginalName string `json:"original_name"`
	SavedName    string `json:"saved_name"`
	SavedPath    string `json:"saved_path"`
	URL          string `json:"url"`
	Size         int64  `json:"size"`
	Type         string `json:"type"`
	Extension    string `json:"extension"`
}

// ValidateFile 验证文件
func ValidateFile(fileHeader *multipart.FileHeader, cfg *config.Config) error {
	// 检查文件大小
	if fileHeader.Size > cfg.Upload.MaxSize {
		return fmt.Errorf("文件大小超过限制，最大允许 %d 字节", cfg.Upload.MaxSize)
	}
	
	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !cfg.IsAllowedFileType(ext) {
		return fmt.Errorf("不支持的文件类型: %s", ext)
	}
	
	// 检查文件名
	if fileHeader.Filename == "" {
		return fmt.Errorf("文件名不能为空")
	}
	
	// 检查文件名长度
	if len(fileHeader.Filename) > 255 {
		return fmt.Errorf("文件名过长，最大允许255个字符")
	}
	
	return nil
}

// ValidateFileContent 验证文件内容
func ValidateFileContent(file multipart.File, filename string) error {
	// 重置文件指针
	file.Seek(0, 0)
	
	// 读取文件头部用于检测文件类型
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return fmt.Errorf("读取文件失败: %w", err)
	}
	
	// 重置文件指针
	file.Seek(0, 0)
	
	// 检测文件类型
	contentType := detectContentType(buffer[:n])
	ext := strings.ToLower(filepath.Ext(filename))
	
	// 验证文件类型与扩展名是否匹配
	if !isValidContentType(contentType, ext) {
		return fmt.Errorf("文件内容与扩展名不匹配")
	}
	
	return nil
}

// detectContentType 检测文件内容类型
func detectContentType(data []byte) string {
	// 检查常见的图片格式
	if len(data) >= 8 {
		// PNG
		if data[0] == 0x89 && data[1] == 0x50 && data[2] == 0x4E && data[3] == 0x47 {
			return "image/png"
		}
		// JPEG
		if data[0] == 0xFF && data[1] == 0xD8 && data[2] == 0xFF {
			return "image/jpeg"
		}
		// GIF
		if data[0] == 0x47 && data[1] == 0x49 && data[2] == 0x46 {
			return "image/gif"
		}
		// WebP
		if data[0] == 0x52 && data[1] == 0x49 && data[2] == 0x46 && data[3] == 0x46 &&
			data[8] == 0x57 && data[9] == 0x45 && data[10] == 0x42 && data[11] == 0x50 {
			return "image/webp"
		}
	}
	
	return "application/octet-stream"
}

// isValidContentType 验证内容类型是否与扩展名匹配
func isValidContentType(contentType, ext string) bool {
	validTypes := map[string][]string{
		".jpg":  {"image/jpeg"},
		".jpeg": {"image/jpeg"},
		".png":  {"image/png"},
		".gif":  {"image/gif"},
		".webp": {"image/webp"},
	}
	
	allowedTypes, exists := validTypes[ext]
	if !exists {
		return false
	}
	
	for _, allowedType := range allowedTypes {
		if contentType == allowedType {
			return true
		}
	}
	
	return false
}

// SaveFile 保存文件
func SaveFile(fileHeader *multipart.FileHeader, cfg *config.Config) (*FileInfo, error) {
	// 打开上传的文件
	file, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()
	
	// 验证文件内容
	if err := ValidateFileContent(file, fileHeader.Filename); err != nil {
		return nil, err
	}
	
	// 生成保存路径
	savePath, savedName, err := generateSavePath(fileHeader.Filename, cfg)
	if err != nil {
		return nil, err
	}
	
	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(savePath), 0755); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}
	
	// 创建目标文件
	dst, err := os.Create(savePath)
	if err != nil {
		return nil, fmt.Errorf("创建文件失败: %w", err)
	}
	defer dst.Close()
	
	// 重置文件指针并复制文件
	file.Seek(0, 0)
	size, err := io.Copy(dst, file)
	if err != nil {
		// 删除部分写入的文件
		os.Remove(savePath)
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}
	
	// 构建文件信息
	fileInfo := &FileInfo{
		OriginalName: fileHeader.Filename,
		SavedName:    savedName,
		SavedPath:    savePath,
		URL:          cfg.Upload.URLPrefix + "/" + savedName,
		Size:         size,
		Type:         detectContentType(nil), // 这里可以优化
		Extension:    filepath.Ext(fileHeader.Filename),
	}
	
	return fileInfo, nil
}

// generateSavePath 生成保存路径
func generateSavePath(originalName string, cfg *config.Config) (string, string, error) {
	// 获取文件扩展名
	ext := filepath.Ext(originalName)
	
	// 生成文件名（使用时间戳和MD5）
	now := time.Now()
	dateDir := now.Format("2006/01/02")
	
	// 生成唯一文件名
	hash := md5.New()
	hash.Write([]byte(fmt.Sprintf("%s_%d", originalName, now.UnixNano())))
	filename := fmt.Sprintf("%x%s", hash.Sum(nil), ext)
	
	// 构建完整路径
	relativePath := filepath.Join(dateDir, filename)
	fullPath := filepath.Join(cfg.Upload.StoragePath, relativePath)
	
	return fullPath, relativePath, nil
}

// DeleteFile 删除文件
func DeleteFile(filePath string, cfg *config.Config) error {
	// 构建完整路径
	fullPath := filepath.Join(cfg.Upload.StoragePath, filePath)
	
	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return nil // 文件不存在，认为删除成功
	}
	
	// 删除文件
	if err := os.Remove(fullPath); err != nil {
		return fmt.Errorf("删除文件失败: %w", err)
	}
	
	return nil
}

// GetFileSize 获取文件大小
func GetFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// FileExists 检查文件是否存在
func FileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

// GetFileExtension 获取文件扩展名
func GetFileExtension(filename string) string {
	return strings.ToLower(filepath.Ext(filename))
}

// IsImageFile 检查是否为图片文件
func IsImageFile(filename string) bool {
	ext := GetFileExtension(filename)
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	
	for _, imageExt := range imageExts {
		if ext == imageExt {
			return true
		}
	}
	
	return false
}
