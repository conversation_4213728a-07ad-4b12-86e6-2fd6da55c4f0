{{template "admin_layout.html" .}}

{{define "content"}}
<!-- 返回按钮 -->
<div class="mb-3">
    <a href="/admin/portfolios" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>返回作品列表
    </a>
</div>

<!-- 表单 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {{if eq .action "create"}}
                    <i class="bi bi-plus-circle me-2"></i>添加作品
                    {{else}}
                    <i class="bi bi-pencil me-2"></i>编辑作品
                    {{end}}
                </h5>
            </div>
            <div class="card-body">
                <form id="portfolioForm">
                    {{if .portfolio}}
                    <input type="hidden" id="portfolioId" value="{{.portfolio.ID}}">
                    {{end}}
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">作品标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required
                               value="{{if .portfolio}}{{.portfolio.Title}}{{end}}"
                               placeholder="请输入作品标题">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">作品描述</label>
                        <textarea class="form-control" id="description" name="description" rows="4"
                                  placeholder="请输入作品描述">{{if .portfolio}}{{.portfolio.Description}}{{end}}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">分类</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">请选择分类</option>
                                    {{range .categories}}
                                    <option value="{{.ID}}" {{if and $.portfolio (eq $.portfolio.CategoryID .ID)}}selected{{end}}>
                                        {{.Name}}
                                    </option>
                                    {{end}}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="draft" {{if and .portfolio (eq .portfolio.Status "draft")}}selected{{end}}>草稿</option>
                                    <option value="published" {{if and .portfolio (eq .portfolio.Status "published")}}selected{{else if not .portfolio}}selected{{end}}>已发布</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">排序顺序</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" min="0"
                               value="{{if .portfolio}}{{.portfolio.SortOrder}}{{else}}0{{end}}"
                               placeholder="数字越小排序越靠前">
                    </div>

                    <div class="mb-3">
                        <label for="cover_image" class="form-label">封面图片 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="cover_image" name="cover_image" required readonly
                                   value="{{if .portfolio}}{{.portfolio.CoverImage}}{{end}}"
                                   placeholder="请上传封面图片">
                            <button type="button" class="btn btn-outline-secondary" onclick="selectCoverImage()">
                                <i class="bi bi-upload me-1"></i>选择图片
                            </button>
                        </div>
                        <div id="coverImagePreview" class="mt-2">
                            {{if .portfolio}}
                            <img src="{{.portfolio.CoverImage}}" class="img-thumbnail" style="max-width: 200px;">
                            {{end}}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">作品图片</label>
                        <div class="border rounded p-3">
                            <div class="mb-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addImageRow()">
                                    <i class="bi bi-plus me-1"></i>添加图片
                                </button>
                            </div>
                            <div id="imagesContainer">
                                {{if .portfolio}}
                                {{range $index, $image := .portfolio.Images}}
                                <div class="image-row mb-2" data-index="{{$index}}">
                                    <div class="row g-2 align-items-center">
                                        <div class="col-md-5">
                                            <input type="text" class="form-control image-url" name="images[{{$index}}][image_url]" 
                                                   value="{{$image.ImageURL}}" placeholder="图片URL" readonly>
                                        </div>
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" name="images[{{$index}}][image_name]" 
                                                   value="{{$image.ImageName}}" placeholder="图片名称">
                                        </div>
                                        <div class="col-md-2">
                                            <input type="number" class="form-control" name="images[{{$index}}][sort_order]" 
                                                   value="{{$image.SortOrder}}" placeholder="排序" min="0">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectImage(this)">
                                                <i class="bi bi-upload"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm ms-1" onclick="removeImageRow(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {{end}}
                                {{end}}
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="/admin/portfolios" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <span class="btn-text">
                                {{if eq .action "create"}}
                                <i class="bi bi-check me-2"></i>创建作品
                                {{else}}
                                <i class="bi bi-check me-2"></i>更新作品
                                {{end}}
                            </span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-2"></span>
                                保存中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 预览区域 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">预览</h6>
            </div>
            <div class="card-body">
                <div id="previewArea">
                    <p class="text-muted text-center">填写表单内容后将显示预览</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文件上传模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="file-upload-area" id="fileUploadArea">
                    <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                    <p class="mb-2">点击选择文件或拖拽文件到此处</p>
                    <p class="small text-muted">支持 JPG、PNG、GIF、WebP 格式，最大 5MB</p>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>
                <div id="uploadProgress" class="mt-3 d-none">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
let imageRowIndex = {{if .portfolio}}{{len .portfolio.Images}}{{else}}0{{end}};
let currentUploadTarget = null;

document.addEventListener('DOMContentLoaded', function() {
    setupForm();
    setupFileUpload();
    updatePreview();
});

function setupForm() {
    const form = document.getElementById('portfolioForm');
    form.addEventListener('submit', handleSubmit);
    
    // 监听表单变化以更新预览
    form.addEventListener('input', updatePreview);
    form.addEventListener('change', updatePreview);
}

function setupFileUpload() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    
    fileUploadArea.addEventListener('click', () => fileInput.click());
    fileUploadArea.addEventListener('dragover', handleDragOver);
    fileUploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const progressBar = document.querySelector('#uploadProgress .progress-bar');
    document.getElementById('uploadProgress').classList.remove('d-none');
    
    try {
        const response = await axios.post('/api/admin/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                progressBar.style.width = percentCompleted + '%';
            }
        });
        
        if (response.data.code === 200) {
            const imageUrl = response.data.data.url;
            if (currentUploadTarget) {
                currentUploadTarget.value = imageUrl;
                if (currentUploadTarget.id === 'cover_image') {
                    updateCoverImagePreview(imageUrl);
                }
                updatePreview();
            }
            showSuccess('图片上传成功');
            bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
        }
    } catch (error) {
        console.error('上传失败:', error);
        showError('图片上传失败');
    } finally {
        document.getElementById('uploadProgress').classList.add('d-none');
        progressBar.style.width = '0%';
    }
}

function selectCoverImage() {
    currentUploadTarget = document.getElementById('cover_image');
    new bootstrap.Modal(document.getElementById('uploadModal')).show();
}

function selectImage(button) {
    const row = button.closest('.image-row');
    currentUploadTarget = row.querySelector('.image-url');
    new bootstrap.Modal(document.getElementById('uploadModal')).show();
}

function updateCoverImagePreview(imageUrl) {
    const preview = document.getElementById('coverImagePreview');
    preview.innerHTML = `<img src="${imageUrl}" class="img-thumbnail" style="max-width: 200px;">`;
}

function addImageRow() {
    const container = document.getElementById('imagesContainer');
    const rowHtml = `
        <div class="image-row mb-2" data-index="${imageRowIndex}">
            <div class="row g-2 align-items-center">
                <div class="col-md-5">
                    <input type="text" class="form-control image-url" name="images[${imageRowIndex}][image_url]" 
                           placeholder="图片URL" readonly>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" name="images[${imageRowIndex}][image_name]" 
                           placeholder="图片名称">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" name="images[${imageRowIndex}][sort_order]" 
                           placeholder="排序" min="0" value="${imageRowIndex}">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectImage(this)">
                        <i class="bi bi-upload"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm ms-1" onclick="removeImageRow(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', rowHtml);
    imageRowIndex++;
}

function removeImageRow(button) {
    button.closest('.image-row').remove();
    updatePreview();
}

function updatePreview() {
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const coverImage = document.getElementById('cover_image').value;
    const status = document.getElementById('status').value;
    
    const previewArea = document.getElementById('previewArea');
    
    if (!title && !description && !coverImage) {
        previewArea.innerHTML = '<p class="text-muted text-center">填写表单内容后将显示预览</p>';
        return;
    }
    
    previewArea.innerHTML = `
        <div class="card">
            ${coverImage ? `<img src="${coverImage}" class="card-img-top" style="height: 150px; object-fit: cover;">` : ''}
            <div class="card-body">
                <h6 class="card-title">${title || '作品标题'}</h6>
                ${description ? `<p class="card-text small text-muted">${description}</p>` : ''}
                <span class="badge ${status === 'published' ? 'bg-success' : 'bg-secondary'} small">
                    ${status === 'published' ? '已发布' : '草稿'}
                </span>
            </div>
        </div>
    `;
}

async function handleSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const portfolioId = document.getElementById('portfolioId')?.value;
    
    // 构建请求数据
    const data = {
        title: formData.get('title'),
        description: formData.get('description'),
        category_id: formData.get('category_id') ? parseInt(formData.get('category_id')) : null,
        cover_image: formData.get('cover_image'),
        status: formData.get('status'),
        sort_order: parseInt(formData.get('sort_order')) || 0,
        images: []
    };
    
    // 收集图片数据
    const imageRows = document.querySelectorAll('.image-row');
    imageRows.forEach((row, index) => {
        const imageUrl = row.querySelector('[name*="[image_url]"]').value;
        const imageName = row.querySelector('[name*="[image_name]"]').value;
        const sortOrder = row.querySelector('[name*="[sort_order]"]').value;
        
        if (imageUrl) {
            data.images.push({
                image_url: imageUrl,
                image_name: imageName || '',
                sort_order: parseInt(sortOrder) || index
            });
        }
    });
    
    setLoading(true);
    
    try {
        let response;
        if (portfolioId) {
            // 更新作品
            response = await axios.put(`/api/admin/portfolios/${portfolioId}`, data);
        } else {
            // 创建作品
            response = await axios.post('/api/admin/portfolios', data);
        }
        
        if (response.data.code === 200) {
            showSuccess(portfolioId ? '作品更新成功' : '作品创建成功');
            setTimeout(() => {
                window.location.href = '/admin/portfolios';
            }, 1500);
        }
    } catch (error) {
        console.error('保存失败:', error);
        if (error.response && error.response.data) {
            showError(error.response.data.message || '保存失败');
        } else {
            showError('保存失败，请稍后重试');
        }
    } finally {
        setLoading(false);
    }
}

function setLoading(loading) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    if (loading) {
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        submitBtn.disabled = true;
    } else {
        btnText.classList.remove('d-none');
        btnLoading.classList.add('d-none');
        submitBtn.disabled = false;
    }
}
</script>
{{end}}
