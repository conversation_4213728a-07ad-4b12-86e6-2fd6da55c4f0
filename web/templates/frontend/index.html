{{template "base.html" .}}

{{define "content"}}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 fw-bold mb-3">作品展示</h1>
                <p class="lead text-muted">展示创意作品，分享设计灵感</p>
            </div>
        </div>
    </div>

    <!-- 分类筛选 -->
    {{if .categories}}
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex flex-wrap justify-content-center gap-2">
                <a href="/" class="btn {{if not .current_category}}btn-primary{{else}}btn-outline-primary{{end}}">
                    全部
                </a>
                {{range .categories}}
                <a href="/category/{{.ID}}" class="btn {{if eq $.current_category .ID}}btn-primary{{else}}btn-outline-primary{{end}}">
                    {{.Name}}
                </a>
                {{end}}
            </div>
        </div>
    </div>
    {{end}}

    <!-- 作品网格 -->
    <div class="row">
        {{if .portfolios}}
        {{range .portfolios}}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 shadow-sm portfolio-card">
                <div class="card-img-wrapper">
                    <img src="{{.CoverImage}}" class="card-img-top" alt="{{.Title}}" loading="lazy">
                    <div class="card-img-overlay d-flex align-items-center justify-content-center opacity-0">
                        <a href="/portfolio/{{.ID}}" class="btn btn-primary btn-lg">
                            <i class="bi bi-eye me-2"></i>查看详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{.Title}}</h5>
                    {{if .Category}}
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="bi bi-tag me-1"></i>{{.Category.Name}}
                        </small>
                    </p>
                    {{end}}
                    {{if .Description}}
                    <p class="card-text text-muted">
                        {{if gt (len .Description) 100}}
                        {{slice .Description 0 100}}...
                        {{else}}
                        {{.Description}}
                        {{end}}
                    </p>
                    {{end}}
                </div>
                <div class="card-footer bg-transparent">
                    <small class="text-muted">
                        <i class="bi bi-calendar me-1"></i>
                        {{.CreatedAt.Format "2006-01-02"}}
                    </small>
                </div>
            </div>
        </div>
        {{end}}
        {{else}}
        <!-- 空状态 -->
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-folder2-open display-1 text-muted mb-3"></i>
                <h3 class="text-muted">暂无作品</h3>
                <p class="text-muted">还没有发布任何作品，请稍后再来查看。</p>
            </div>
        </div>
        {{end}}
    </div>

    <!-- 分页 -->
    {{if .pagination}}
    {{with .pagination}}
    {{if gt .total_pages 1}}
    <div class="row mt-5">
        <div class="col-12">
            <nav aria-label="作品分页">
                <ul class="pagination justify-content-center">
                    {{if .has_prev}}
                    <li class="page-item">
                        <a class="page-link" href="?page={{.prev_page}}{{if $.current_category}}&category={{$.current_category}}{{end}}">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                    {{else}}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="bi bi-chevron-left"></i>
                        </span>
                    </li>
                    {{end}}

                    {{range $i := iterate .total_pages}}
                    {{$page := add $i 1}}
                    {{if eq $page $.current_page}}
                    <li class="page-item active">
                        <span class="page-link">{{$page}}</span>
                    </li>
                    {{else}}
                    <li class="page-item">
                        <a class="page-link" href="?page={{$page}}{{if $.current_category}}&category={{$.current_category}}{{end}}">{{$page}}</a>
                    </li>
                    {{end}}
                    {{end}}

                    {{if .has_next}}
                    <li class="page-item">
                        <a class="page-link" href="?page={{.next_page}}{{if $.current_category}}&category={{$.current_category}}{{end}}">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                    {{else}}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="bi bi-chevron-right"></i>
                        </span>
                    </li>
                    {{end}}
                </ul>
            </nav>
        </div>
    </div>
    {{end}}
    {{end}}
    {{end}}
</div>
{{end}}

{{define "scripts"}}
<script>
// 作品卡片悬停效果
document.addEventListener('DOMContentLoaded', function() {
    const portfolioCards = document.querySelectorAll('.portfolio-card');
    
    portfolioCards.forEach(card => {
        const overlay = card.querySelector('.card-img-overlay');
        
        card.addEventListener('mouseenter', function() {
            overlay.classList.remove('opacity-0');
            overlay.classList.add('opacity-100');
        });
        
        card.addEventListener('mouseleave', function() {
            overlay.classList.remove('opacity-100');
            overlay.classList.add('opacity-0');
        });
    });
});
</script>
{{end}}
