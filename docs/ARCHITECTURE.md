# 系统架构设计文档

## 1. 整体架构概览

### 1.1 架构模式
采用**前后端分离**的架构模式，具有以下特点：
- 前端负责用户界面展示和交互
- 后端提供RESTful API服务
- 数据库存储业务数据
- 通过HTTP/HTTPS协议进行通信

### 1.2 系统分层

```
┌─────────────────────────────────────────────────────────────┐
│                    表现层 (Presentation Layer)               │
├─────────────────────┬───────────────────┬───────────────────┤
│     前台展示页面     │    管理后台页面    │     移动端适配     │
│   (Vue.js/HTML)     │   (Vue.js/HTML)   │   (响应式设计)     │
└─────────────────────┴───────────────────┴───────────────────┘
                                │
                    ┌─────────────────┐
                    │   API网关层      │
                    │   (Nginx)       │
                    └─────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────┬───────────────────┬───────────────────┤
│     路由层          │    中间件层        │    处理器层        │
│   (Gin Router)      │  (Auth/CORS/Log)  │   (HTTP Handler)  │
└─────────────────────┴───────────────────┴───────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (Business Layer)                   │
├─────────────────────┬───────────────────┬───────────────────┤
│     服务层          │    模型层          │    仓储层          │
│   (Service)         │   (Model)         │  (Repository)     │
└─────────────────────┴───────────────────┴───────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据层 (Data Layer)                       │
├─────────────────────┬───────────────────┬───────────────────┤
│     数据库          │    文件存储        │     缓存          │
│    (MySQL)          │  (Local/Cloud)    │    (Redis)        │
└─────────────────────┴───────────────────┴───────────────────┘
```

## 2. 核心组件设计

### 2.1 Web服务层 (Gin Framework)

**职责**：
- HTTP请求路由
- 中间件处理
- 请求响应处理
- 静态资源服务

**关键组件**：
```go
// 路由配置
func SetupRouter() *gin.Engine {
    r := gin.New()
    
    // 中间件
    r.Use(middleware.Logger())
    r.Use(middleware.CORS())
    r.Use(middleware.Recovery())
    
    // API路由组
    v1 := r.Group("/api/v1")
    {
        // 公开接口
        v1.GET("/portfolios", handler.GetPortfolios)
        v1.GET("/portfolios/:id", handler.GetPortfolio)
        v1.GET("/portfolios/featured", handler.GetFeaturedPortfolios)

        // 认证接口
        auth := v1.Group("/auth")
        {
            auth.POST("/login", handler.Login)
            auth.POST("/logout", handler.Logout)
            auth.GET("/profile", middleware.AuthRequired(), handler.GetProfile)
        }

        // 管理接口（需要认证）
        admin := v1.Group("/admin")
        admin.Use(middleware.AuthRequired())
        {
            // 作品管理
            admin.GET("/portfolios", handler.GetAdminPortfolios)
            admin.POST("/portfolios", handler.CreatePortfolio)
            admin.GET("/portfolios/:id", handler.GetAdminPortfolio)
            admin.PUT("/portfolios/:id", handler.UpdatePortfolio)
            admin.DELETE("/portfolios/:id", handler.DeletePortfolio)
        }

        // 文件上传接口（需要认证）
        upload := v1.Group("/uploads")
        upload.Use(middleware.AuthRequired())
        {
            upload.POST("", handler.UploadFile)
            upload.POST("/batch", handler.BatchUploadFiles)
        }
    }
    
    return r
}
```

### 2.2 业务服务层 (Service Layer)

**职责**：
- 业务逻辑处理
- 数据验证
- 事务管理
- 外部服务调用

**设计模式**：
```go
type PortfolioService interface {
    GetList(req *GetListRequest) (*PaginatedResponse, error)
    GetByID(id uint) (*Portfolio, error)
    Create(req *CreateRequest) (*Portfolio, error)
    Update(id uint, req *UpdateRequest) (*Portfolio, error)
    Delete(id uint) error
}

type portfolioService struct {
    repo   repository.PortfolioRepository
    upload service.UploadService
}

func (s *portfolioService) Create(req *CreateRequest) (*Portfolio, error) {
    // 1. 数据验证
    if err := s.validateCreateRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 处理图片上传
    imageURLs, err := s.upload.ProcessImages(req.Images)
    if err != nil {
        return nil, err
    }
    
    // 3. 创建作品记录
    portfolio := &model.Portfolio{
        Title:       req.Title,
        Description: req.Description,
        Tags:        req.Tags,
        Images:      imageURLs,
    }
    
    return s.repo.Create(portfolio)
}
```

### 2.3 数据访问层 (Repository Layer)

**职责**：
- 数据库操作封装
- 查询优化
- 事务处理
- 数据映射

**设计模式**：
```go
type PortfolioRepository interface {
    GetList(offset, limit int, categoryID uint) ([]*Portfolio, int64, error)
    GetByID(id uint) (*Portfolio, error)
    Create(portfolio *Portfolio) error
    Update(portfolio *Portfolio) error
    Delete(id uint) error
}

type portfolioRepository struct {
    db *gorm.DB
}

func (r *portfolioRepository) GetList(offset, limit int, categoryID uint) ([]*Portfolio, int64, error) {
    var portfolios []*Portfolio
    var total int64
    
    query := r.db.Model(&Portfolio{}).Where("status = ?", "published")
    
    // 获取总数
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 获取数据
    err := query.Order("sort_order DESC, created_at DESC").
        Offset(offset).
        Limit(limit).
        Find(&portfolios).Error
    
    return portfolios, total, err
}
```

## 3. 数据模型设计

### 3.1 实体关系图

```
┌─────────────┐                         ┌─────────────┐
│    Users    │                         │ Portfolios  │
├─────────────┤                         ├─────────────┤
│ id (PK)     │                         │ id (PK)     │
│ username    │                         │ title       │
│ password    │                         │ description │
│ email       │                         │ content     │
│ role        │                         │ cover_image │
│ created_at  │                         │ images      │
│ updated_at  │                         │ tags        │
└─────────────┘                         │ status      │
                                        │ view_count  │
                                        │ sort_order  │
                                        │ created_at  │
                                        │ updated_at  │
                                        └─────────────┘
                                               │
                                               ▼
                                    ┌─────────────────┐
                                    │ Portfolio_Images│
                                    ├─────────────────┤
                                    │ id (PK)         │
                                    │ portfolio_id    │
                                    │ image_url       │
                                    │ alt_text        │
                                    │ sort_order      │
                                    │ created_at      │
                                    └─────────────────┘
```

### 3.2 数据模型定义

```go
// 用户模型
type User struct {
    ID        uint      `gorm:"primaryKey" json:"id"`
    Username  string    `gorm:"uniqueIndex;size:50" json:"username"`
    Password  string    `gorm:"size:255" json:"-"`
    Email     string    `gorm:"size:100" json:"email"`
    Avatar    string    `gorm:"size:255" json:"avatar"`
    Role      string    `gorm:"type:enum('admin','user');default:'user'" json:"role"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}



// 作品模型
type Portfolio struct {
    ID          uint      `gorm:"primaryKey" json:"id"`
    Title       string    `gorm:"size:200" json:"title"`
    Description string    `gorm:"type:text" json:"description"`
    Content     string    `gorm:"type:longtext" json:"content"`
    CoverImage  string    `gorm:"size:255" json:"cover_image"`
    Images      JSON      `gorm:"type:json" json:"images"`
    Tags        string    `gorm:"size:500" json:"tags"`
    Status      string    `gorm:"type:enum('draft','published');default:'draft'" json:"status"`
    ViewCount   int       `gorm:"default:0" json:"view_count"`
    SortOrder   int       `gorm:"default:0" json:"sort_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`

    // 关联
    Images   []PortfolioImage `gorm:"foreignKey:PortfolioID" json:"portfolio_images,omitempty"`
}
```

## 4. 安全架构

### 4.1 认证授权

```go
// JWT中间件
func AuthRequired() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(401, gin.H{"error": "未授权访问"})
            c.Abort()
            return
        }
        
        // 验证JWT Token
        claims, err := jwt.ValidateToken(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "Token无效"})
            c.Abort()
            return
        }
        
        c.Set("user_id", claims.UserID)
        c.Set("role", claims.Role)
        c.Next()
    }
}
```

### 4.2 数据验证

```go
// 输入验证
type CreatePortfolioRequest struct {
    Title       string `json:"title" binding:"required,min=1,max=200"`
    Description string `json:"description" binding:"max=1000"`
    CategoryID  uint   `json:"category_id" binding:"required,gt=0"`
    Images      []string `json:"images" binding:"required,min=1"`
}

func (h *PortfolioHandler) Create(c *gin.Context) {
    var req CreatePortfolioRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": "参数验证失败", "details": err.Error()})
        return
    }
    
    // 业务逻辑处理...
}
```

## 5. 性能优化策略

### 5.1 数据库优化

```sql
-- 索引优化
CREATE INDEX idx_portfolios_category_status ON portfolios(category_id, status);
CREATE INDEX idx_portfolios_created_at ON portfolios(created_at DESC);
CREATE INDEX idx_portfolios_sort_order ON portfolios(sort_order DESC);

-- 查询优化
SELECT p.*, c.name as category_name 
FROM portfolios p 
LEFT JOIN categories c ON p.category_id = c.id 
WHERE p.status = 'published' 
ORDER BY p.sort_order DESC, p.created_at DESC 
LIMIT 10 OFFSET 0;
```

### 5.2 缓存策略

```go
// Redis缓存
type CacheService interface {
    Get(key string) (string, error)
    Set(key string, value interface{}, expiration time.Duration) error
    Delete(key string) error
}

func (s *portfolioService) GetList(req *GetListRequest) (*PaginatedResponse, error) {
    // 生成缓存键
    cacheKey := fmt.Sprintf("portfolios:list:%d:%d:%d", req.Page, req.PageSize, req.CategoryID)
    
    // 尝试从缓存获取
    if cached, err := s.cache.Get(cacheKey); err == nil {
        var result PaginatedResponse
        if json.Unmarshal([]byte(cached), &result) == nil {
            return &result, nil
        }
    }
    
    // 从数据库获取
    result, err := s.repo.GetList(req)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    if data, err := json.Marshal(result); err == nil {
        s.cache.Set(cacheKey, string(data), 5*time.Minute)
    }
    
    return result, nil
}
```

## 6. 部署架构

### 6.1 容器化部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
  
  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: portfolio
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  mysql_data:
  redis_data:
```

### 6.2 监控和日志

```go
// 结构化日志
func setupLogger() *logrus.Logger {
    logger := logrus.New()
    logger.SetFormatter(&logrus.JSONFormatter{})
    logger.SetLevel(logrus.InfoLevel)
    
    // 日志文件轮转
    hook, err := rotatelogs.New(
        "logs/app.%Y%m%d.log",
        rotatelogs.WithMaxAge(7*24*time.Hour),
        rotatelogs.WithRotationTime(24*time.Hour),
    )
    if err == nil {
        logger.AddHook(lfshook.NewHook(hook, &logrus.JSONFormatter{}))
    }
    
    return logger
}

// 性能监控中间件
func MetricsMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start)
        
        // 记录请求指标
        metrics.RequestDuration.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            strconv.Itoa(c.Writer.Status()),
        ).Observe(duration.Seconds())
        
        metrics.RequestTotal.WithLabelValues(
            c.Request.Method,
            c.FullPath(),
            strconv.Itoa(c.Writer.Status()),
        ).Inc()
    }
}
```

这个架构设计文档涵盖了系统的各个层面，为后续的开发实施提供了详细的技术指导。
