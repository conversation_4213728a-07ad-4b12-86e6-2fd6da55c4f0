package utils

import (
	"fmt"
	"log"
	"portfolio-website/internal/model"
	"time"

	"gorm.io/gorm"
)

// Migration 迁移接口
type Migration interface {
	Up() error
	Down() error
	Version() string
	Description() string
}

// MigrationManager 迁移管理器
type MigrationManager struct {
	db         *gorm.DB
	migrations []Migration
}

// MigrationRecord 迁移记录
type MigrationRecord struct {
	ID          uint      `gorm:"primaryKey"`
	Version     string    `gorm:"size:50;not null;uniqueIndex"`
	Description string    `gorm:"size:255"`
	AppliedAt   time.Time `gorm:"not null"`
}

// TableName 指定表名
func (MigrationRecord) TableName() string {
	return "migrations"
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB) *MigrationManager {
	return &MigrationManager{
		db:         db,
		migrations: []Migration{},
	}
}

// AddMigration 添加迁移
func (m *MigrationManager) AddMigration(migration Migration) {
	m.migrations = append(m.migrations, migration)
}

// RunMigrations 运行所有迁移
func (m *MigrationManager) RunMigrations() error {
	// 确保迁移表存在
	if err := m.db.AutoMigrate(&MigrationRecord{}); err != nil {
		return fmt.Errorf("创建迁移表失败: %w", err)
	}
	
	// 获取已应用的迁移
	appliedMigrations, err := m.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("获取已应用迁移失败: %w", err)
	}
	
	// 运行未应用的迁移
	for _, migration := range m.migrations {
		if !m.isMigrationApplied(migration.Version(), appliedMigrations) {
			log.Printf("应用迁移: %s - %s", migration.Version(), migration.Description())
			
			if err := migration.Up(); err != nil {
				return fmt.Errorf("迁移 %s 失败: %w", migration.Version(), err)
			}
			
			// 记录迁移
			record := MigrationRecord{
				Version:     migration.Version(),
				Description: migration.Description(),
				AppliedAt:   time.Now(),
			}
			if err := m.db.Create(&record).Error; err != nil {
				return fmt.Errorf("记录迁移失败: %w", err)
			}
			
			log.Printf("迁移 %s 应用成功", migration.Version())
		}
	}
	
	return nil
}

// RollbackMigration 回滚迁移
func (m *MigrationManager) RollbackMigration(version string) error {
	// 查找迁移
	var migration Migration
	for _, m := range m.migrations {
		if m.Version() == version {
			migration = m
			break
		}
	}
	
	if migration == nil {
		return fmt.Errorf("未找到版本 %s 的迁移", version)
	}
	
	// 检查迁移是否已应用
	var record MigrationRecord
	if err := m.db.Where("version = ?", version).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("迁移 %s 未应用，无法回滚", version)
		}
		return fmt.Errorf("查询迁移记录失败: %w", err)
	}
	
	log.Printf("回滚迁移: %s - %s", migration.Version(), migration.Description())
	
	// 执行回滚
	if err := migration.Down(); err != nil {
		return fmt.Errorf("回滚迁移 %s 失败: %w", version, err)
	}
	
	// 删除迁移记录
	if err := m.db.Delete(&record).Error; err != nil {
		return fmt.Errorf("删除迁移记录失败: %w", err)
	}
	
	log.Printf("迁移 %s 回滚成功", version)
	return nil
}

// GetMigrationStatus 获取迁移状态
func (m *MigrationManager) GetMigrationStatus() ([]MigrationStatus, error) {
	appliedMigrations, err := m.getAppliedMigrations()
	if err != nil {
		return nil, err
	}
	
	var status []MigrationStatus
	for _, migration := range m.migrations {
		s := MigrationStatus{
			Version:     migration.Version(),
			Description: migration.Description(),
			Applied:     m.isMigrationApplied(migration.Version(), appliedMigrations),
		}
		
		if s.Applied {
			for _, applied := range appliedMigrations {
				if applied.Version == migration.Version() {
					s.AppliedAt = &applied.AppliedAt
					break
				}
			}
		}
		
		status = append(status, s)
	}
	
	return status, nil
}

// MigrationStatus 迁移状态
type MigrationStatus struct {
	Version     string     `json:"version"`
	Description string     `json:"description"`
	Applied     bool       `json:"applied"`
	AppliedAt   *time.Time `json:"applied_at,omitempty"`
}

// getAppliedMigrations 获取已应用的迁移
func (m *MigrationManager) getAppliedMigrations() ([]MigrationRecord, error) {
	var records []MigrationRecord
	if err := m.db.Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// isMigrationApplied 检查迁移是否已应用
func (m *MigrationManager) isMigrationApplied(version string, appliedMigrations []MigrationRecord) bool {
	for _, applied := range appliedMigrations {
		if applied.Version == version {
			return true
		}
	}
	return false
}

// InitialMigration 初始迁移
type InitialMigration struct {
	db *gorm.DB
}

// NewInitialMigration 创建初始迁移
func NewInitialMigration(db *gorm.DB) *InitialMigration {
	return &InitialMigration{db: db}
}

// Up 执行迁移
func (m *InitialMigration) Up() error {
	// 自动迁移所有模型
	return m.db.AutoMigrate(
		&model.Category{},
		&model.Portfolio{},
		&model.PortfolioImage{},
	)
}

// Down 回滚迁移
func (m *InitialMigration) Down() error {
	// 删除所有表（谨慎使用）
	return m.db.Migrator().DropTable(
		&model.PortfolioImage{},
		&model.Portfolio{},
		&model.Category{},
	)
}

// Version 返回版本号
func (m *InitialMigration) Version() string {
	return "20240101_000001"
}

// Description 返回描述
func (m *InitialMigration) Description() string {
	return "Initial migration - create categories, portfolios, and portfolio_images tables"
}
