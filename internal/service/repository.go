package service

import (
	"portfolio-website/internal/model"

	"gorm.io/gorm"
)

// Repository 仓储接口
type Repository interface {
	CategoryRepository
	PortfolioRepository
}

// CategoryRepository 分类仓储接口
type CategoryRepository interface {
	CreateCategory(category *model.Category) error
	GetCategoryByID(id uint) (*model.Category, error)
	GetCategories() ([]*model.Category, error)
	UpdateCategory(category *model.Category) error
	DeleteCategory(id uint) error
	GetCategoryWithPortfolioCount(id uint) (*model.Category, int64, error)
}

// PortfolioRepository 作品仓储接口
type PortfolioRepository interface {
	CreatePortfolio(portfolio *model.Portfolio) error
	GetPortfolioByID(id uint) (*model.Portfolio, error)
	GetPortfolios(page, pageSize int, categoryID uint, status string) ([]*model.Portfolio, int64, error)
	UpdatePortfolio(portfolio *model.Portfolio) error
	DeletePortfolio(id uint) error
	GetPortfoliosByCategory(categoryID uint, page, pageSize int) ([]*model.Portfolio, int64, error)
}

// repository 仓储实现
type repository struct {
	db *gorm.DB
}

// NewRepository 创建仓储实例
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// CreateCategory 创建分类
func (r *repository) CreateCategory(category *model.Category) error {
	return r.db.Create(category).Error
}

// GetCategoryByID 根据ID获取分类
func (r *repository) GetCategoryByID(id uint) (*model.Category, error) {
	var category model.Category
	err := r.db.First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// GetCategories 获取所有分类
func (r *repository) GetCategories() ([]*model.Category, error) {
	var categories []*model.Category
	err := r.db.Order("sort_order ASC, created_at ASC").Find(&categories).Error
	return categories, err
}

// UpdateCategory 更新分类
func (r *repository) UpdateCategory(category *model.Category) error {
	return r.db.Save(category).Error
}

// DeleteCategory 删除分类
func (r *repository) DeleteCategory(id uint) error {
	return r.db.Delete(&model.Category{}, id).Error
}

// GetCategoryWithPortfolioCount 获取分类及其作品数量
func (r *repository) GetCategoryWithPortfolioCount(id uint) (*model.Category, int64, error) {
	var category model.Category
	err := r.db.First(&category, id).Error
	if err != nil {
		return nil, 0, err
	}
	
	var count int64
	err = r.db.Model(&model.Portfolio{}).Where("category_id = ? AND status = ?", id, model.PortfolioStatusPublished).Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	
	return &category, count, nil
}

// CreatePortfolio 创建作品
func (r *repository) CreatePortfolio(portfolio *model.Portfolio) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 创建作品
		if err := tx.Create(portfolio).Error; err != nil {
			return err
		}
		
		// 创建作品图片
		if len(portfolio.Images) > 0 {
			for i := range portfolio.Images {
				portfolio.Images[i].PortfolioID = portfolio.ID
				portfolio.Images[i].ID = 0 // 确保ID为0，让数据库自动生成
			}
			if err := tx.Create(&portfolio.Images).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}

// GetPortfolioByID 根据ID获取作品
func (r *repository) GetPortfolioByID(id uint) (*model.Portfolio, error) {
	var portfolio model.Portfolio
	err := r.db.Preload("Category").Preload("Images", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort_order ASC, created_at ASC")
	}).First(&portfolio, id).Error
	if err != nil {
		return nil, err
	}
	return &portfolio, nil
}

// GetPortfolios 获取作品列表
func (r *repository) GetPortfolios(page, pageSize int, categoryID uint, status string) ([]*model.Portfolio, int64, error) {
	var portfolios []*model.Portfolio
	var total int64

	query := r.db.Model(&model.Portfolio{})

	// 添加条件
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 计算总数（优化：使用相同的查询条件）
	countQuery := r.db.Model(&model.Portfolio{})
	if categoryID > 0 {
		countQuery = countQuery.Where("category_id = ?", categoryID)
	}
	if status != "" {
		countQuery = countQuery.Where("status = ?", status)
	}
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询（优化：只查询需要的字段）
	offset := (page - 1) * pageSize
	err := query.Select("id, title, description, category_id, cover_image, sort_order, status, created_at, updated_at").
		Preload("Category", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name")
		}).
		Order("sort_order ASC, created_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&portfolios).Error

	return portfolios, total, err
}

// UpdatePortfolio 更新作品
func (r *repository) UpdatePortfolio(portfolio *model.Portfolio) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 更新作品基本信息
		if err := tx.Save(portfolio).Error; err != nil {
			return err
		}
		
		// 删除旧的图片记录
		if err := tx.Where("portfolio_id = ?", portfolio.ID).Delete(&model.PortfolioImage{}).Error; err != nil {
			return err
		}
		
		// 创建新的图片记录
		if len(portfolio.Images) > 0 {
			for i := range portfolio.Images {
				portfolio.Images[i].PortfolioID = portfolio.ID
				portfolio.Images[i].ID = 0 // 确保创建新记录
			}
			if err := tx.Create(&portfolio.Images).Error; err != nil {
				return err
			}
		}
		
		return nil
	})
}

// DeletePortfolio 删除作品
func (r *repository) DeletePortfolio(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除作品图片
		if err := tx.Where("portfolio_id = ?", id).Delete(&model.PortfolioImage{}).Error; err != nil {
			return err
		}
		
		// 删除作品
		if err := tx.Delete(&model.Portfolio{}, id).Error; err != nil {
			return err
		}
		
		return nil
	})
}

// GetPortfoliosByCategory 根据分类获取作品
func (r *repository) GetPortfoliosByCategory(categoryID uint, page, pageSize int) ([]*model.Portfolio, int64, error) {
	return r.GetPortfolios(page, pageSize, categoryID, model.PortfolioStatusPublished)
}
