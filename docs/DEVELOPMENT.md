# 作品展示网站开发文档

## 项目概述

这是一个基于Go语言开发的作品展示网站，采用前后端分离架构，提供作品展示和后台管理功能。

## 技术栈

### 后端
- **语言**: Go 1.19+
- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL 8.0+
- **认证**: JWT
- **配置管理**: Viper
- **日志**: Logrus
- **图片处理**: imaging

### 前端
- **基础版**: HTML5 + CSS3 + JavaScript
- **进阶版**: Vue.js 3 + TypeScript
- **HTTP客户端**: Axios

### 部署
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: Systemd

## 环境搭建

### 1. 开发环境要求
- Go 1.19+
- MySQL 8.0+
- Node.js 16+ (如果使用Vue.js前端)
- Git

### 2. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd portfolio-website

# 安装Go依赖
go mod tidy

# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 3. 环境变量配置

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=portfolio

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE_HOURS=24

# 服务器配置
SERVER_PORT=8080
SERVER_MODE=debug

# 文件上传配置
UPLOAD_PATH=./web/uploads
MAX_UPLOAD_SIZE=10485760  # 10MB

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

### 4. 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE portfolio CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
go run cmd/server/main.go migrate
```

### 5. 启动开发服务器

```bash
# 启动后端服务
go run cmd/server/main.go

# 如果使用Vue.js前端，在另一个终端启动前端开发服务器
cd web/frontend
npm install
npm run dev
```

## 开发规范

### 1. 代码结构规范

- **cmd/**: 应用程序入口点
- **internal/**: 私有应用程序代码
- **web/**: Web资源（静态文件、模板等）
- **docs/**: 项目文档
- **scripts/**: 构建和部署脚本

### 2. 命名规范

- **包名**: 小写，简短，有意义
- **文件名**: 小写，使用下划线分隔
- **变量名**: 驼峰命名法
- **常量名**: 大写，使用下划线分隔
- **函数名**: 驼峰命名法，公开函数首字母大写

### 3. 错误处理

```go
// 统一错误响应格式
type ErrorResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    any    `json:"data,omitempty"`
}

// 错误处理示例
func (h *PortfolioHandler) GetPortfolio(c *gin.Context) {
    id := c.Param("id")
    portfolio, err := h.service.GetByID(id)
    if err != nil {
        c.JSON(http.StatusNotFound, ErrorResponse{
            Code:    404,
            Message: "作品不存在",
        })
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "data": portfolio,
    })
}
```

### 4. 数据库操作规范

```go
// 使用事务处理复杂操作
func (r *PortfolioRepository) CreateWithImages(portfolio *model.Portfolio, images []string) error {
    return r.db.Transaction(func(tx *gorm.DB) error {
        if err := tx.Create(portfolio).Error; err != nil {
            return err
        }
        
        for i, imageURL := range images {
            image := &model.PortfolioImage{
                PortfolioID: portfolio.ID,
                ImageURL:    imageURL,
                SortOrder:   i,
            }
            if err := tx.Create(image).Error; err != nil {
                return err
            }
        }
        
        return nil
    })
}
```

### 5. API响应格式

```json
// 成功响应
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    }
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "data": null
}

// 分页响应
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [],
        "total": 100,
        "page": 1,
        "page_size": 10,
        "total_pages": 10
    }
}
```

## 测试

### 1. 单元测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并显示覆盖率
go test -cover ./...
```

### 2. API测试

使用Postman或curl进行API测试：

```bash
# 获取作品列表
curl -X GET "http://localhost:8080/api/v1/portfolios?page=1&page_size=10"

# 获取单个作品
curl -X GET "http://localhost:8080/api/v1/portfolios/1"

# 获取推荐作品
curl -X GET "http://localhost:8080/api/v1/portfolios/featured?limit=6"

# 管理员登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# 创建作品（需要认证）
curl -X POST "http://localhost:8080/api/v1/admin/portfolios" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"title":"新作品","description":"作品描述","tags":"设计,网页"}'

# 更新作品（需要认证）
curl -X PUT "http://localhost:8080/api/v1/admin/portfolios/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"title":"更新的作品标题"}'

# 删除作品（需要认证）
curl -X DELETE "http://localhost:8080/api/v1/admin/portfolios/1" \
  -H "Authorization: Bearer {token}"

# 上传图片（需要认证）
curl -X POST "http://localhost:8080/api/v1/uploads" \
  -H "Authorization: Bearer {token}" \
  -F "file=@/path/to/image.jpg"
```

## 部署

### 1. Docker部署

```bash
# 构建镜像
docker build -t portfolio-website .

# 使用Docker Compose启动
docker-compose up -d
```

### 2. 生产环境部署

```bash
# 编译生产版本
CGO_ENABLED=0 GOOS=linux go build -o portfolio-server cmd/server/main.go

# 使用systemd管理服务
sudo cp scripts/portfolio.service /etc/systemd/system/
sudo systemctl enable portfolio
sudo systemctl start portfolio
```

## 常见问题

### 1. 数据库连接失败
- 检查数据库服务是否启动
- 验证连接参数是否正确
- 确认数据库用户权限

### 2. 文件上传失败
- 检查上传目录权限
- 验证文件大小限制
- 确认磁盘空间充足

### 3. JWT认证失败
- 检查JWT密钥配置
- 验证token是否过期
- 确认请求头格式正确

## 开发工具推荐

- **IDE**: VS Code + Go扩展
- **API测试**: Postman
- **数据库管理**: MySQL Workbench
- **版本控制**: Git
- **容器管理**: Docker Desktop
