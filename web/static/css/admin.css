/* 管理后台样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
}

/* 登录页面样式 */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 侧边栏样式 */
.sidebar {
    min-height: 100vh;
    background: #343a40;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #adb5bd;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #fff;
    background: rgba(255,255,255,0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background: #007bff;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* 主内容区域 */
.main-content {
    min-height: 100vh;
    padding: 0;
}

.content-header {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
    padding: 20px 30px;
    margin-bottom: 0;
}

.content-body {
    padding: 30px;
}

/* 顶部导航栏 */
.top-navbar {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 30px;
}

.user-menu .dropdown-toggle::after {
    display: none;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 20px;
    font-weight: 600;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* 文件上传样式 */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #007bff;
    background: rgba(0,123,255,0.05);
}

.file-upload-area.dragover {
    border-color: #007bff;
    background: rgba(0,123,255,0.1);
}

/* 图片预览样式 */
.image-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.image-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.image-item .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-item:hover .overlay {
    opacity: 1;
}

/* 状态标签样式 */
.badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
}

.status-published {
    background: #28a745;
}

.status-draft {
    background: #6c757d;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 8px 12px;
}

.pagination .page-link:hover {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.pagination .page-item.active .page-link {
    background: #007bff;
    border-color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content-header,
    .content-body {
        padding: 15px;
    }
    
    .top-navbar {
        padding: 10px 15px;
    }
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* 工具提示样式 */
.tooltip {
    font-size: 12px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
