package middleware

import (
	"compress/gzip"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
)

// Gzip 压缩中间件
func Gzip() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查客户端是否支持gzip
		if !strings.Contains(c.<PERSON>eader("Accept-Encoding"), "gzip") {
			c.Next()
			return
		}
		
		// 检查内容类型是否需要压缩
		if !shouldCompress(c.GetHeader("Content-Type")) {
			c.Next()
			return
		}
		
		// 设置响应头
		c.Header("Content-Encoding", "gzip")
		c.<PERSON><PERSON>("Vary", "Accept-Encoding")
		
		// 创建gzip writer
		gz := gzip.NewWriter(c.Writer)
		defer gz.Close()
		
		// 包装ResponseWriter
		c.Writer = &gzipWriter{
			ResponseWriter: c.Writer,
			Writer:         gz,
		}
		
		c.Next()
	}
}

// gzipWriter 包装ResponseWriter以支持gzip压缩
type gzipWriter struct {
	gin.ResponseWriter
	Writer io.Writer
}

// Write 写入压缩数据
func (g *gzipWriter) Write(data []byte) (int, error) {
	return g.Writer.Write(data)
}

// WriteString 写入压缩字符串
func (g *gzipWriter) WriteString(s string) (int, error) {
	return g.Writer.Write([]byte(s))
}

// shouldCompress 判断是否应该压缩
func shouldCompress(contentType string) bool {
	// 需要压缩的内容类型
	compressibleTypes := []string{
		"text/html",
		"text/css",
		"text/javascript",
		"application/javascript",
		"application/json",
		"application/xml",
		"text/xml",
		"text/plain",
		"image/svg+xml",
	}
	
	contentType = strings.ToLower(contentType)
	for _, t := range compressibleTypes {
		if strings.Contains(contentType, t) {
			return true
		}
	}
	
	return false
}
