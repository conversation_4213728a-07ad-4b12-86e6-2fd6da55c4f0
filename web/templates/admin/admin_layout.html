<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="/static/css/admin.css" rel="stylesheet">
    
    {{block "head" .}}{{end}}
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="bi bi-palette me-2"></i>
                            管理后台
                        </h4>
                    </div>
                    
                    <!-- 导航菜单 -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/">
                                <i class="bi bi-speedometer2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/portfolios">
                                <i class="bi bi-collection"></i>
                                作品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/categories">
                                <i class="bi bi-tags"></i>
                                分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/" target="_blank">
                                <i class="bi bi-eye"></i>
                                查看前台
                            </a>
                        </li>
                    </ul>
                    
                    <!-- 用户信息 -->
                    <div class="mt-auto pt-3 border-top border-secondary">
                        <div class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{.page_title}}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary d-md-none" id="sidebarToggle">
                            <i class="bi bi-list"></i>
                        </button>
                    </div>
                </div>

                <!-- 页面内容 -->
                <div class="content-body">
                    {{block "content" .}}{{end}}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios for API calls -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="/static/js/admin.js"></script>
    
    <script>
    // 退出登录
    function logout() {
        if (confirm('确定要退出登录吗？')) {
            // 清除cookie
            document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            // 跳转到登录页
            window.location.href = '/admin/login';
        }
    }
    </script>
    
    {{block "scripts" .}}{{end}}
</body>
</html>
