# 作品展示网站 (Portfolio Website)

一个基于Go语言开发的现代化作品展示网站，采用前后端分离架构，提供优雅的作品展示和强大的后台管理功能。

## ✨ 项目特性

### 🎨 前台展示
- **响应式设计**: 完美适配桌面端、平板和移动设备
- **作品展示**: 瀑布流/网格布局展示作品集
- **作品详情**: 高清图片展示，支持图片放大查看
- **标签筛选**: 支持按标签浏览作品
- **SEO优化**: 搜索引擎友好的URL和元数据

### 🛠️ 后台管理
- **作品管理**: 创建、编辑、删除作品
- **批量上传**: 支持多图片批量上传
- **标签管理**: 灵活的作品标签系统
- **图片处理**: 自动生成缩略图，优化加载速度
- **数据统计**: 作品浏览量统计
- **用户管理**: 管理员账户管理

### 🚀 技术亮点
- **高性能**: Go语言原生性能，Gin框架轻量高效
- **安全可靠**: JWT认证，SQL注入防护，XSS防护
- **易于部署**: Docker容器化部署，一键启动
- **可扩展**: 模块化设计，易于功能扩展
- **标准化**: RESTful API设计，前后端完全分离

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端展示页面   │    │   管理后台页面   │    │   移动端适配     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Nginx反向代理  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Go后端服务     │
                    │   (Gin框架)     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库    │
                    └─────────────────┘
```

## 🛠️ 技术栈

| 分类 | 技术选型 | 说明 |
|------|----------|------|
| **后端语言** | Go 1.19+ | 高性能、并发友好 |
| **Web框架** | Gin | 轻量级、高性能HTTP框架 |
| **数据库** | MySQL 8.0+ | 稳定可靠的关系型数据库 |
| **ORM** | GORM | Go最流行的ORM框架 |
| **认证** | JWT | 无状态认证方案 |
| **前端** | Vue.js 3 / 原生JS | 现代化前端框架 |
| **容器化** | Docker | 简化部署和运维 |
| **反向代理** | Nginx | 高性能Web服务器 |

## 📦 快速开始

### 环境要求
- Go 1.19+
- MySQL 8.0+
- Docker & Docker Compose (可选)

### 1. 克隆项目
```bash
git clone https://github.com/your-username/portfolio-website.git
cd portfolio-website
```

### 2. 配置环境
```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，设置数据库连接等信息
vim .env
```

### 3. 初始化数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE portfolio CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
go run cmd/server/main.go migrate
```

### 4. 启动服务
```bash
# 方式一：直接运行
go run cmd/server/main.go

# 方式二：Docker部署
docker-compose up -d
```

### 5. 访问应用
- 前台展示: http://localhost:8080
- 管理后台: http://localhost:8080/admin
- API文档: http://localhost:8080/swagger

## 📁 项目结构

```
portfolio-website/
├── cmd/server/          # 应用入口
├── internal/            # 核心业务逻辑
│   ├── config/         # 配置管理
│   ├── handler/        # HTTP处理器
│   ├── middleware/     # 中间件
│   ├── model/          # 数据模型
│   ├── repository/     # 数据访问层
│   ├── service/        # 业务逻辑层
│   └── router/         # 路由配置
├── web/                # 前端资源
│   ├── static/         # 静态文件
│   ├── templates/      # HTML模板
│   └── uploads/        # 上传文件
├── docs/               # 项目文档
├── migrations/         # 数据库迁移
└── scripts/            # 部署脚本
```

## 🔧 核心功能

### 作品管理
- ✅ 作品CRUD操作
- ✅ 多图片上传
- ✅ 图片自动压缩和缩略图生成
- ✅ 作品分类管理
- ✅ 作品排序和置顶
- ✅ 草稿和发布状态管理

### 用户体验
- ✅ 响应式设计
- ✅ 图片懒加载
- ✅ 无限滚动加载
- ✅ 图片预览和放大
- ✅ 标签筛选
- ✅ SEO优化

### 系统管理
- ✅ JWT身份认证
- ✅ 权限控制
- ✅ 操作日志
- ✅ 数据备份
- ✅ 性能监控

## 📊 API接口

### 前台接口
```
GET  /api/v1/portfolios              # 获取作品列表
GET  /api/v1/portfolios/{id}         # 获取作品详情
GET  /api/v1/portfolios/featured     # 获取推荐作品
```

### 管理接口
```
POST   /api/v1/auth/login              # 管理员登录
GET    /api/v1/admin/portfolios        # 管理作品列表
POST   /api/v1/admin/portfolios        # 创建作品
GET    /api/v1/admin/portfolios/{id}   # 获取作品详情
PUT    /api/v1/admin/portfolios/{id}   # 更新作品
DELETE /api/v1/admin/portfolios/{id}   # 删除作品
POST   /api/v1/uploads                 # 上传图片
POST   /api/v1/uploads/batch           # 批量上传图片
```

## 🚀 部署指南

### Docker部署（推荐）
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 传统部署
```bash
# 编译应用
go build -o portfolio-server cmd/server/main.go

# 配置systemd服务
sudo cp scripts/portfolio.service /etc/systemd/system/
sudo systemctl enable portfolio
sudo systemctl start portfolio

# 配置Nginx反向代理
sudo cp scripts/nginx.conf /etc/nginx/sites-available/portfolio
sudo ln -s /etc/nginx/sites-available/portfolio /etc/nginx/sites-enabled/
sudo systemctl reload nginx
```

## 📈 性能优化

- **数据库优化**: 合理的索引设计，查询优化
- **图片优化**: 自动压缩，WebP格式支持，CDN加速
- **缓存策略**: Redis缓存热点数据，静态资源缓存
- **并发处理**: Go协程处理并发请求
- **资源压缩**: Gzip压缩，资源合并

## 🔒 安全特性

- **身份认证**: JWT Token认证
- **权限控制**: 基于角色的访问控制
- **数据验证**: 输入参数严格验证
- **SQL注入防护**: GORM参数化查询
- **XSS防护**: 输出内容转义
- **CSRF防护**: CSRF Token验证
- **文件上传安全**: 文件类型和大小限制

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址: https://github.com/your-username/portfolio-website
- 问题反馈: https://github.com/your-username/portfolio-website/issues
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者们！

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
