{{template "admin_layout.html" .}}

{{define "content"}}
<!-- 操作栏 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0">作品管理</h4>
        <small class="text-muted">管理您的所有作品</small>
    </div>
    <div>
        <a href="/admin/portfolios/create" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>添加作品
        </a>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">搜索</label>
                <input type="text" class="form-control" id="searchInput" placeholder="搜索作品标题...">
            </div>
            <div class="col-md-3">
                <label class="form-label">分类筛选</label>
                <select class="form-select" id="categoryFilter">
                    <option value="">所有分类</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">状态筛选</label>
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="published">已发布</option>
                    <option value="draft">草稿</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 作品列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="60">封面</th>
                        <th>标题</th>
                        <th width="120">分类</th>
                        <th width="80">状态</th>
                        <th width="120">创建时间</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody id="portfoliosTableBody">
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="作品分页" id="paginationNav" class="d-none">
            <ul class="pagination justify-content-center mb-0" id="pagination">
            </ul>
        </nav>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除作品 "<span id="deletePortfolioTitle"></span>" 吗？</p>
                <p class="text-danger small">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
let portfolios = [];
let categories = [];
let filteredPortfolios = [];
let currentPage = 1;
const pageSize = 10;

document.addEventListener('DOMContentLoaded', function() {
    loadData();
    setupEventListeners();
});

async function loadData() {
    try {
        const [portfoliosRes, categoriesRes] = await Promise.all([
            axios.get('/api/admin/portfolios'),
            axios.get('/api/admin/categories')
        ]);

        portfolios = portfoliosRes.data.data || [];
        categories = categoriesRes.data.data || [];
        
        setupCategoryFilter();
        applyFilters();
    } catch (error) {
        console.error('加载数据失败:', error);
        showError('加载数据失败');
    }
}

function setupCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    categoryFilter.innerHTML = '<option value="">所有分类</option>';
    
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categoryFilter.appendChild(option);
    });
}

function setupEventListeners() {
    document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
}

function applyFilters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryId = document.getElementById('categoryFilter').value;
    const status = document.getElementById('statusFilter').value;

    filteredPortfolios = portfolios.filter(portfolio => {
        const matchesSearch = portfolio.title.toLowerCase().includes(searchTerm);
        const matchesCategory = !categoryId || portfolio.category_id == categoryId;
        const matchesStatus = !status || portfolio.status === status;
        
        return matchesSearch && matchesCategory && matchesStatus;
    });

    currentPage = 1;
    renderPortfolios();
    renderPagination();
}

function renderPortfolios() {
    const tbody = document.getElementById('portfoliosTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pagePortfolios = filteredPortfolios.slice(startIndex, endIndex);

    if (pagePortfolios.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted py-4">没有找到作品</td></tr>';
        return;
    }

    tbody.innerHTML = pagePortfolios.map(portfolio => `
        <tr>
            <td>
                <img src="${portfolio.cover_image}" class="rounded" width="50" height="50" style="object-fit: cover;">
            </td>
            <td>
                <div>
                    <strong>${portfolio.title}</strong>
                    ${portfolio.description ? `<br><small class="text-muted">${truncateText(portfolio.description, 50)}</small>` : ''}
                </div>
            </td>
            <td>
                ${portfolio.category ? `<span class="badge bg-light text-dark">${portfolio.category.name}</span>` : '<span class="text-muted">无分类</span>'}
            </td>
            <td>
                <span class="badge ${portfolio.status === 'published' ? 'bg-success' : 'bg-secondary'}">
                    ${portfolio.status === 'published' ? '已发布' : '草稿'}
                </span>
            </td>
            <td>
                <small>${formatDate(portfolio.created_at)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/portfolio/${portfolio.id}" target="_blank" class="btn btn-outline-info" title="预览">
                        <i class="bi bi-eye"></i>
                    </a>
                    <a href="/admin/portfolios/${portfolio.id}/edit" class="btn btn-outline-primary" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </a>
                    <button type="button" class="btn btn-outline-danger" onclick="showDeleteModal(${portfolio.id}, '${portfolio.title}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function renderPagination() {
    const totalPages = Math.ceil(filteredPortfolios.length / pageSize);
    const paginationNav = document.getElementById('paginationNav');
    const pagination = document.getElementById('pagination');

    if (totalPages <= 1) {
        paginationNav.classList.add('d-none');
        return;
    }

    paginationNav.classList.remove('d-none');
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = paginationHTML;
}

function changePage(page) {
    const totalPages = Math.ceil(filteredPortfolios.length / pageSize);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderPortfolios();
    renderPagination();
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    applyFilters();
}

function showDeleteModal(id, title) {
    document.getElementById('deletePortfolioTitle').textContent = title;
    document.getElementById('confirmDeleteBtn').onclick = () => deletePortfolio(id);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

async function deletePortfolio(id) {
    try {
        await axios.delete(`/api/admin/portfolios/${id}`);
        showSuccess('作品删除成功');
        bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        loadData(); // 重新加载数据
    } catch (error) {
        console.error('删除作品失败:', error);
        showError('删除作品失败');
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{{end}}
