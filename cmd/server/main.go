package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"portfolio-website/internal/config"
	"portfolio-website/internal/handler"
	"portfolio-website/internal/model"
	"portfolio-website/internal/service"
	"portfolio-website/internal/utils"
)

const (
	Version = "1.0.0"
	AppName = "Portfolio Website"
)

func main() {
	// 打印启动信息
	fmt.Printf("%s v%s\n", AppName, Version)
	fmt.Println("正在启动服务器...")

	// 加载配置
	cfg, err := config.Load("configs/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	if err := utils.InitializeDatabase(cfg); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer func() {
		if err := utils.CloseDatabase(); err != nil {
			log.Printf("关闭数据库连接失败: %v", err)
		}
	}()

	// 测试数据库连接
	if err := utils.TestDatabaseConnection(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	// 初始化会话服务
	service.InitSessionService()

	// 初始化缓存
	utils.InitCache()

	// 初始化服务层
	repo := service.NewRepository(model.GetDB())
	portfolioService := service.NewPortfolioService(repo)
	categoryService := service.NewCategoryService(repo)

	// 初始化路由
	router := handler.NewRouter(cfg, portfolioService, categoryService)
	router.SetupRoutes()

	fmt.Printf("服务器启动成功，监听端口: %d\n", cfg.Server.Port)
	fmt.Printf("访问地址: http://localhost:%d\n", cfg.Server.Port)
	fmt.Printf("管理后台: http://localhost:%d/admin\n", cfg.Server.Port)

	// 启动HTTP服务器
	go func() {
		if err := router.Run(); err != nil {
			log.Fatalf("启动HTTP服务器失败: %v", err)
		}
	}()

	// 等待中断信号
	waitForShutdown()

	fmt.Println("服务器已关闭")
}

// waitForShutdown 等待关闭信号
func waitForShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	fmt.Println("\n正在关闭服务器...")
}
