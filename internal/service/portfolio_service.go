package service

import (
	"errors"
	"fmt"
	"portfolio-website/internal/model"

	"gorm.io/gorm"
)

// PortfolioService 作品服务接口
type PortfolioService interface {
	CreatePortfolio(req *model.PortfolioCreateRequest) (*model.Portfolio, error)
	GetPortfolioByID(id uint) (*model.Portfolio, error)
	GetPortfolios(page, pageSize int, categoryID uint) ([]*model.Portfolio, int64, error)
	GetPublishedPortfolios(page, pageSize int, categoryID uint) ([]*model.Portfolio, int64, error)
	UpdatePortfolio(id uint, req *model.PortfolioUpdateRequest) (*model.Portfolio, error)
	DeletePortfolio(id uint) error
	GetPortfoliosByCategory(categoryID uint, page, pageSize int) ([]*model.Portfolio, int64, error)
}

// portfolioService 作品服务实现
type portfolioService struct {
	repo Repository
}

// NewPortfolioService 创建作品服务
func NewPortfolioService(repo Repository) PortfolioService {
	return &portfolioService{repo: repo}
}

// CreatePortfolio 创建作品
func (s *portfolioService) CreatePortfolio(req *model.PortfolioCreateRequest) (*model.Portfolio, error) {
	// 验证分类是否存在
	if req.CategoryID != nil {
		_, err := s.repo.GetCategoryByID(*req.CategoryID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("分类不存在")
			}
			return nil, fmt.Errorf("验证分类失败: %w", err)
		}
	}
	
	// 构建作品对象
	portfolio := &model.Portfolio{
		Title:       req.Title,
		Description: req.Description,
		CategoryID:  req.CategoryID,
		CoverImage:  req.CoverImage,
		Status:      req.Status,
		SortOrder:   req.SortOrder,
	}
	
	// 构建图片对象
	if len(req.Images) > 0 {
		portfolio.Images = make([]model.PortfolioImage, len(req.Images))
		for i, imgReq := range req.Images {
			portfolio.Images[i] = model.PortfolioImage{
				ImageURL:  imgReq.ImageURL,
				ImageName: imgReq.ImageName,
				SortOrder: imgReq.SortOrder,
			}
		}
	}
	
	// 创建作品
	if err := s.repo.CreatePortfolio(portfolio); err != nil {
		return nil, fmt.Errorf("创建作品失败: %w", err)
	}
	
	// 重新获取完整的作品信息
	return s.repo.GetPortfolioByID(portfolio.ID)
}

// GetPortfolioByID 根据ID获取作品
func (s *portfolioService) GetPortfolioByID(id uint) (*model.Portfolio, error) {
	portfolio, err := s.repo.GetPortfolioByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("作品不存在")
		}
		return nil, fmt.Errorf("获取作品失败: %w", err)
	}
	return portfolio, nil
}

// GetPortfolios 获取作品列表（管理后台用）
func (s *portfolioService) GetPortfolios(page, pageSize int, categoryID uint) ([]*model.Portfolio, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	
	return s.repo.GetPortfolios(page, pageSize, categoryID, "")
}

// GetPublishedPortfolios 获取已发布的作品列表（前台用）
func (s *portfolioService) GetPublishedPortfolios(page, pageSize int, categoryID uint) ([]*model.Portfolio, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 12
	}
	
	return s.repo.GetPortfolios(page, pageSize, categoryID, model.PortfolioStatusPublished)
}

// UpdatePortfolio 更新作品
func (s *portfolioService) UpdatePortfolio(id uint, req *model.PortfolioUpdateRequest) (*model.Portfolio, error) {
	// 检查作品是否存在
	existingPortfolio, err := s.repo.GetPortfolioByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("作品不存在")
		}
		return nil, fmt.Errorf("获取作品失败: %w", err)
	}
	
	// 验证分类是否存在
	if req.CategoryID != nil {
		_, err := s.repo.GetCategoryByID(*req.CategoryID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("分类不存在")
			}
			return nil, fmt.Errorf("验证分类失败: %w", err)
		}
	}
	
	// 更新作品信息
	existingPortfolio.Title = req.Title
	existingPortfolio.Description = req.Description
	existingPortfolio.CategoryID = req.CategoryID
	existingPortfolio.CoverImage = req.CoverImage
	existingPortfolio.Status = req.Status
	existingPortfolio.SortOrder = req.SortOrder
	
	// 更新图片信息
	if len(req.Images) > 0 {
		existingPortfolio.Images = make([]model.PortfolioImage, len(req.Images))
		for i, imgReq := range req.Images {
			existingPortfolio.Images[i] = model.PortfolioImage{
				ImageURL:  imgReq.ImageURL,
				ImageName: imgReq.ImageName,
				SortOrder: imgReq.SortOrder,
			}
		}
	} else {
		existingPortfolio.Images = []model.PortfolioImage{}
	}
	
	// 保存更新
	if err := s.repo.UpdatePortfolio(existingPortfolio); err != nil {
		return nil, fmt.Errorf("更新作品失败: %w", err)
	}
	
	// 重新获取完整的作品信息
	return s.repo.GetPortfolioByID(id)
}

// DeletePortfolio 删除作品
func (s *portfolioService) DeletePortfolio(id uint) error {
	// 检查作品是否存在
	_, err := s.repo.GetPortfolioByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("作品不存在")
		}
		return fmt.Errorf("获取作品失败: %w", err)
	}
	
	// 删除作品
	if err := s.repo.DeletePortfolio(id); err != nil {
		return fmt.Errorf("删除作品失败: %w", err)
	}
	
	return nil
}

// GetPortfoliosByCategory 根据分类获取作品
func (s *portfolioService) GetPortfoliosByCategory(categoryID uint, page, pageSize int) ([]*model.Portfolio, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 12
	}
	
	// 验证分类是否存在
	_, err := s.repo.GetCategoryByID(categoryID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, 0, fmt.Errorf("分类不存在")
		}
		return nil, 0, fmt.Errorf("验证分类失败: %w", err)
	}
	
	return s.repo.GetPortfoliosByCategory(categoryID, page, pageSize)
}
