package service

import (
	"errors"
	"fmt"
	"portfolio-website/internal/model"
	"portfolio-website/internal/utils"
	"time"

	"gorm.io/gorm"
)

// CategoryService 分类服务接口
type CategoryService interface {
	CreateCategory(req *model.CategoryCreateRequest) (*model.Category, error)
	GetCategoryByID(id uint) (*model.Category, error)
	GetCategories() ([]*model.Category, error)
	GetCategoriesWithCount() ([]*model.CategoryResponse, error)
	UpdateCategory(id uint, req *model.CategoryUpdateRequest) (*model.Category, error)
	DeleteCategory(id uint) error
}

// categoryService 分类服务实现
type categoryService struct {
	repo Repository
}

// NewCategoryService 创建分类服务
func NewCategoryService(repo Repository) CategoryService {
	return &categoryService{repo: repo}
}

// CreateCategory 创建分类
func (s *categoryService) CreateCategory(req *model.CategoryCreateRequest) (*model.Category, error) {
	// 构建分类对象
	category := &model.Category{
		Name:        req.Name,
		Description: req.Description,
		SortOrder:   req.SortOrder,
	}
	
	// 创建分类
	if err := s.repo.CreateCategory(category); err != nil {
		return nil, fmt.Errorf("创建分类失败: %w", err)
	}
	
	return category, nil
}

// GetCategoryByID 根据ID获取分类
func (s *categoryService) GetCategoryByID(id uint) (*model.Category, error) {
	category, err := s.repo.GetCategoryByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("分类不存在")
		}
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}
	return category, nil
}

// GetCategories 获取所有分类
func (s *categoryService) GetCategories() ([]*model.Category, error) {
	// 尝试从缓存获取
	cache := utils.GetCache()
	cacheKey := utils.CacheKey("categories", "all")

	if cached, found := cache.Get(cacheKey); found {
		if categories, ok := cached.([]*model.Category); ok {
			return categories, nil
		}
	}

	// 从数据库获取
	categories, err := s.repo.GetCategories()
	if err != nil {
		return nil, fmt.Errorf("获取分类列表失败: %w", err)
	}

	// 缓存结果（5分钟）
	cache.Set(cacheKey, categories, 5*time.Minute)

	return categories, nil
}

// GetCategoriesWithCount 获取分类及作品数量
func (s *categoryService) GetCategoriesWithCount() ([]*model.CategoryResponse, error) {
	categories, err := s.repo.GetCategories()
	if err != nil {
		return nil, fmt.Errorf("获取分类列表失败: %w", err)
	}
	
	var responses []*model.CategoryResponse
	for _, category := range categories {
		_, count, err := s.repo.GetCategoryWithPortfolioCount(category.ID)
		if err != nil {
			return nil, fmt.Errorf("获取分类作品数量失败: %w", err)
		}
		
		response := category.ToResponseWithCount(count)
		responses = append(responses, response)
	}
	
	return responses, nil
}

// UpdateCategory 更新分类
func (s *categoryService) UpdateCategory(id uint, req *model.CategoryUpdateRequest) (*model.Category, error) {
	// 检查分类是否存在
	existingCategory, err := s.repo.GetCategoryByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("分类不存在")
		}
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}
	
	// 更新分类信息
	existingCategory.Name = req.Name
	existingCategory.Description = req.Description
	existingCategory.SortOrder = req.SortOrder
	
	// 保存更新
	if err := s.repo.UpdateCategory(existingCategory); err != nil {
		return nil, fmt.Errorf("更新分类失败: %w", err)
	}
	
	return existingCategory, nil
}

// DeleteCategory 删除分类
func (s *categoryService) DeleteCategory(id uint) error {
	// 检查分类是否存在
	_, err := s.repo.GetCategoryByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("分类不存在")
		}
		return fmt.Errorf("获取分类失败: %w", err)
	}
	
	// 检查是否有作品使用此分类
	_, count, err := s.repo.GetCategoryWithPortfolioCount(id)
	if err != nil {
		return fmt.Errorf("检查分类使用情况失败: %w", err)
	}
	
	if count > 0 {
		return fmt.Errorf("该分类下还有 %d 个作品，无法删除", count)
	}
	
	// 删除分类
	if err := s.repo.DeleteCategory(id); err != nil {
		return fmt.Errorf("删除分类失败: %w", err)
	}
	
	return nil
}
