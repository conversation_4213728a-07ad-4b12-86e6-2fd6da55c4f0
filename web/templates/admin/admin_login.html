<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100">
            <div class="col-md-6 col-lg-4 mx-auto">
                <div class="login-card p-5">
                    <div class="text-center mb-4">
                        <div class="logo-icon">
                            <i class="bi bi-palette text-white" style="font-size: 2rem;"></i>
                        </div>
                        <h2 class="fw-bold text-dark mb-2">管理员登录</h2>
                        <p class="text-muted">欢迎回来，请输入您的密码</p>
                    </div>

                    <!-- 错误提示 -->
                    <div id="errorAlert" class="alert alert-danger d-none" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>

                    <!-- 登录表单 -->
                    <form id="loginForm">
                        <div class="mb-4">
                            <label for="password" class="form-label fw-semibold">
                                <i class="bi bi-key me-2"></i>管理员密码
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password"
                                       required placeholder="请输入管理员密码">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                <span class="btn-text">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    登录中...
                                </span>
                            </button>
                        </div>

                        <div class="text-center">
                            <a href="/" class="text-decoration-none text-muted">
                                <i class="bi bi-arrow-left me-1"></i>返回前台
                            </a>
                        </div>
                    </form>
                </div>

                <div class="text-center mt-4">
                    <small class="text-white-50">
                        © 2024 作品展示系统 | 现代化管理界面
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('loginForm');
        const passwordInput = document.getElementById('password');
        const togglePasswordBtn = document.getElementById('togglePassword');
        const loginBtn = document.getElementById('loginBtn');
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');

        // 密码显示/隐藏
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            const icon = this.querySelector('i');
            icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
        });

        // 表单提交
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const password = passwordInput.value.trim();
            if (!password) {
                showError('请输入密码');
                return;
            }

            setLoading(true);
            hideError();

            try {
                const response = await axios.post('/api/admin/login', {
                    password: password
                });

                if (response.data.code === 200) {
                    showSuccess('登录成功，正在跳转...');
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 1000);
                } else {
                    showError(response.data.message || '登录失败');
                }
            } catch (error) {
                console.error('Login error:', error);
                if (error.response && error.response.data) {
                    showError(error.response.data.message || '登录失败');
                } else {
                    showError('网络错误，请稍后重试');
                }
            } finally {
                setLoading(false);
            }
        });

        function showError(message) {
            errorMessage.textContent = message;
            errorAlert.className = 'alert alert-danger';
            errorAlert.classList.remove('d-none');
        }

        function showSuccess(message) {
            errorMessage.innerHTML = '<i class="bi bi-check-circle me-2"></i>' + message;
            errorAlert.className = 'alert alert-success';
            errorAlert.classList.remove('d-none');
        }

        function hideError() {
            errorAlert.classList.add('d-none');
        }

        function setLoading(loading) {
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');

            if (loading) {
                btnText.classList.add('d-none');
                btnLoading.classList.remove('d-none');
                loginBtn.disabled = true;
                passwordInput.disabled = true;
            } else {
                btnText.classList.remove('d-none');
                btnLoading.classList.add('d-none');
                loginBtn.disabled = false;
                passwordInput.disabled = false;
            }
        }

        // 自动聚焦
        passwordInput.focus();
    });
    </script>
</body>
</html>

{{define "content"}}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- 左侧装饰 -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
            <div class="text-center text-white">
                <i class="bi bi-palette display-1 mb-4"></i>
                <h2 class="fw-bold mb-3">作品展示管理系统</h2>
                <p class="lead">管理您的创意作品，展示设计才华</p>
            </div>
        </div>
        
        <!-- 右侧登录表单 -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-shield-lock display-4 text-primary mb-3"></i>
                            <h3 class="fw-bold">管理员登录</h3>
                            <p class="text-muted">请输入管理员密码</p>
                        </div>
                        
                        <!-- 错误提示 -->
                        <div id="errorAlert" class="alert alert-danger d-none" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <span id="errorMessage"></span>
                        </div>
                        
                        <!-- 登录表单 -->
                        <form id="loginForm">
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="bi bi-key me-2"></i>密码
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" 
                                           id="password" name="password" required 
                                           placeholder="请输入管理员密码">
                                    <button class="btn btn-outline-secondary" type="button" 
                                            id="togglePassword" aria-label="显示/隐藏密码">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                    <span class="btn-text">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>登录
                                    </span>
                                    <span class="btn-loading d-none">
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        登录中...
                                    </span>
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <a href="/" class="text-decoration-none">
                                    <i class="bi bi-arrow-left me-1"></i>返回前台
                                </a>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- 版权信息 -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        © 2024 作品展示系统 | Powered by Go & Bootstrap
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const passwordInput = document.getElementById('password');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const loginBtn = document.getElementById('loginBtn');
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    // 密码显示/隐藏切换
    togglePasswordBtn.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
    });
    
    // 表单提交
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const password = passwordInput.value.trim();
        if (!password) {
            showError('请输入密码');
            return;
        }
        
        // 显示加载状态
        setLoading(true);
        hideError();
        
        try {
            const response = await axios.post('/api/admin/login', {
                password: password
            });
            
            if (response.data.code === 200) {
                // 登录成功，跳转到管理后台
                showSuccess('登录成功，正在跳转...');
                setTimeout(() => {
                    window.location.href = '/admin/portfolios';
                }, 1000);
            } else {
                showError(response.data.message || '登录失败');
            }
        } catch (error) {
            console.error('Login error:', error);
            if (error.response && error.response.data) {
                showError(error.response.data.message || '登录失败');
            } else {
                showError('网络错误，请稍后重试');
            }
        } finally {
            setLoading(false);
        }
    });
    
    // 显示错误信息
    function showError(message) {
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        errorAlert.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    // 隐藏错误信息
    function hideError() {
        errorAlert.classList.add('d-none');
    }
    
    // 显示成功信息
    function showSuccess(message) {
        errorAlert.className = 'alert alert-success';
        errorMessage.innerHTML = '<i class="bi bi-check-circle me-2"></i>' + message;
        errorAlert.classList.remove('d-none');
    }
    
    // 设置加载状态
    function setLoading(loading) {
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            loginBtn.disabled = true;
            passwordInput.disabled = true;
        } else {
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            loginBtn.disabled = false;
            passwordInput.disabled = false;
        }
    }
    
    // 自动聚焦密码输入框
    passwordInput.focus();
    
    // 回车键提交
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginForm.dispatchEvent(new Event('submit'));
        }
    });
});
</script>
{{end}}
