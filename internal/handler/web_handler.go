package handler

import (
	"net/http"
	"portfolio-website/internal/config"
	"portfolio-website/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// WebHandler 前台页面处理器
type WebHandler struct {
	portfolioService service.PortfolioService
	categoryService  service.CategoryService
	config           *config.Config
}

// NewWebHandler 创建前台页面处理器
func NewWebHandler(portfolioService service.PortfolioService, categoryService service.CategoryService, cfg *config.Config) *WebHandler {
	return &WebHandler{
		portfolioService: portfolioService,
		categoryService:  categoryService,
		config:           cfg,
	}
}

// Index 首页
func (h *WebHandler) Index(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	categoryID, _ := strconv.ParseUint(c.Query("category"), 10, 32)
	
	// 获取作品列表
	portfolios, total, err := h.portfolioService.GetPublishedPortfolios(page, h.config.Pagination.PageSize, uint(categoryID))
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title":   "服务器错误",
			"message": "获取作品列表失败",
		})
		return
	}
	
	// 获取分类列表
	categories, err := h.categoryService.GetCategories()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title":   "服务器错误",
			"message": "获取分类列表失败",
		})
		return
	}
	
	// 计算分页信息
	totalPages := int((total + int64(h.config.Pagination.PageSize) - 1) / int64(h.config.Pagination.PageSize))
	if totalPages < 1 {
		totalPages = 1
	}
	
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title":       h.config.Site.Title,
		"description": h.config.Site.Description,
		"keywords":    h.config.Site.Keywords,
		"portfolios":  portfolios,
		"categories":  categories,
		"pagination": gin.H{
			"current_page": page,
			"total_pages":  totalPages,
			"has_prev":     page > 1,
			"has_next":     page < totalPages,
			"prev_page":    page - 1,
			"next_page":    page + 1,
		},
		"current_category": categoryID,
	})
}

// PortfolioDetail 作品详情页
func (h *WebHandler) PortfolioDetail(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title":   "页面不存在",
			"message": "无效的作品ID",
		})
		return
	}
	
	// 获取作品详情
	portfolio, err := h.portfolioService.GetPortfolioByID(uint(id))
	if err != nil || !portfolio.IsPublished() {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title":   "页面不存在",
			"message": "作品不存在或未发布",
		})
		return
	}
	
	c.HTML(http.StatusOK, "portfolio_detail.html", gin.H{
		"title":       portfolio.Title + " - " + h.config.Site.Title,
		"description": portfolio.Description,
		"portfolio":   portfolio,
	})
}

// CategoryPortfolios 分类作品页
func (h *WebHandler) CategoryPortfolios(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	categoryID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title":   "页面不存在",
			"message": "无效的分类ID",
		})
		return
	}
	
	// 获取分类信息
	category, err := h.categoryService.GetCategoryByID(uint(categoryID))
	if err != nil {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title":   "页面不存在",
			"message": "分类不存在",
		})
		return
	}
	
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	
	// 获取分类下的作品
	portfolios, total, err := h.portfolioService.GetPortfoliosByCategory(uint(categoryID), page, h.config.Pagination.PageSize)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title":   "服务器错误",
			"message": "获取作品列表失败",
		})
		return
	}
	
	// 获取所有分类（用于导航）
	categories, err := h.categoryService.GetCategories()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title":   "服务器错误",
			"message": "获取分类列表失败",
		})
		return
	}
	
	// 计算分页信息
	totalPages := int((total + int64(h.config.Pagination.PageSize) - 1) / int64(h.config.Pagination.PageSize))
	if totalPages < 1 {
		totalPages = 1
	}
	
	c.HTML(http.StatusOK, "category_portfolios.html", gin.H{
		"title":       category.Name + " - " + h.config.Site.Title,
		"description": category.Description,
		"category":    category,
		"portfolios":  portfolios,
		"categories":  categories,
		"pagination": gin.H{
			"current_page": page,
			"total_pages":  totalPages,
			"has_prev":     page > 1,
			"has_next":     page < totalPages,
			"prev_page":    page - 1,
			"next_page":    page + 1,
		},
	})
}

// AdminIndex 管理后台首页
func (h *WebHandler) AdminIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title":      "管理后台 - " + h.config.Site.Title,
		"page_title": "仪表盘",
	})
}

// AdminLogin 管理员登录页
func (h *WebHandler) AdminLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "admin_login.html", gin.H{
		"title": "管理员登录 - " + h.config.Site.Title,
	})
}

// AdminPortfolios 作品管理页
func (h *WebHandler) AdminPortfolios(c *gin.Context) {
	c.HTML(http.StatusOK, "admin_portfolios.html", gin.H{
		"title":      "作品管理 - " + h.config.Site.Title,
		"page_title": "作品管理",
	})
}

// AdminCreatePortfolio 创建作品页
func (h *WebHandler) AdminCreatePortfolio(c *gin.Context) {
	// 获取分类列表
	categories, err := h.categoryService.GetCategories()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title":   "服务器错误",
			"message": "获取分类列表失败",
		})
		return
	}

	c.HTML(http.StatusOK, "admin_portfolio_form.html", gin.H{
		"title":      "创建作品 - " + h.config.Site.Title,
		"page_title": "创建作品",
		"categories": categories,
		"action":     "create",
	})
}

// AdminEditPortfolio 编辑作品页
func (h *WebHandler) AdminEditPortfolio(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title":   "页面不存在",
			"message": "无效的作品ID",
		})
		return
	}
	
	// 获取作品详情
	portfolio, err := h.portfolioService.GetPortfolioByID(uint(id))
	if err != nil {
		c.HTML(http.StatusNotFound, "404.html", gin.H{
			"title":   "页面不存在",
			"message": "作品不存在",
		})
		return
	}
	
	// 获取分类列表
	categories, err := h.categoryService.GetCategories()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title":   "服务器错误",
			"message": "获取分类列表失败",
		})
		return
	}
	
	c.HTML(http.StatusOK, "admin_portfolio_form.html", gin.H{
		"title":      "编辑作品 - " + h.config.Site.Title,
		"page_title": "编辑作品",
		"portfolio":  portfolio,
		"categories": categories,
		"action":     "edit",
	})
}

// AdminCategories 分类管理页
func (h *WebHandler) AdminCategories(c *gin.Context) {
	c.HTML(http.StatusOK, "admin_categories.html", gin.H{
		"title":      "分类管理 - " + h.config.Site.Title,
		"page_title": "分类管理",
	})
}
