{{template "base.html" .}}

{{define "content"}}
<div class="container">
    {{with .portfolio}}
    <!-- 返回按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>返回
            </a>
        </div>
    </div>

    <!-- 作品标题和信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-5 fw-bold mb-3">{{.Title}}</h1>
                <div class="d-flex justify-content-center align-items-center gap-3 text-muted">
                    {{if .Category}}
                    <span>
                        <i class="bi bi-tag me-1"></i>{{.Category.Name}}
                    </span>
                    {{end}}
                    <span>
                        <i class="bi bi-calendar me-1"></i>{{.CreatedAt.Format "2006-01-02"}}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 作品描述 -->
    {{if .Description}}
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <p class="card-text lead">{{.Description}}</p>
                </div>
            </div>
        </div>
    </div>
    {{end}}

    <!-- 作品图片 -->
    <div class="row">
        <div class="col-12">
            {{if .Images}}
            <!-- 图片轮播 -->
            <div id="portfolioCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                <div class="carousel-indicators">
                    {{range $index, $image := .Images}}
                    <button type="button" data-bs-target="#portfolioCarousel" data-bs-slide-to="{{$index}}" 
                            {{if eq $index 0}}class="active"{{end}}></button>
                    {{end}}
                </div>
                
                <div class="carousel-inner">
                    {{range $index, $image := .Images}}
                    <div class="carousel-item {{if eq $index 0}}active{{end}}">
                        <img src="{{$image.ImageURL}}" class="d-block w-100 portfolio-image" 
                             alt="{{if $image.ImageName}}{{$image.ImageName}}{{else}}{{$.Title}}{{end}}"
                             data-bs-toggle="modal" data-bs-target="#imageModal" 
                             data-bs-image="{{$image.ImageURL}}"
                             data-bs-title="{{if $image.ImageName}}{{$image.ImageName}}{{else}}{{$.Title}}{{end}}">
                    </div>
                    {{end}}
                </div>
                
                {{if gt (len .Images) 1}}
                <button class="carousel-control-prev" type="button" data-bs-target="#portfolioCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#portfolioCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
                {{end}}
            </div>

            <!-- 缩略图 -->
            {{if gt (len .Images) 1}}
            <div class="row g-2 mb-4">
                {{range $index, $image := .Images}}
                <div class="col-2 col-md-1">
                    <img src="{{$image.ImageURL}}" class="img-thumbnail portfolio-thumb" 
                         alt="{{if $image.ImageName}}{{$image.ImageName}}{{else}}{{$.Title}}{{end}}"
                         data-bs-target="#portfolioCarousel" data-bs-slide-to="{{$index}}"
                         style="cursor: pointer;">
                </div>
                {{end}}
            </div>
            {{end}}
            {{else}}
            <!-- 只有封面图 -->
            <div class="text-center mb-4">
                <img src="{{.CoverImage}}" class="img-fluid portfolio-image" alt="{{.Title}}"
                     data-bs-toggle="modal" data-bs-target="#imageModal" 
                     data-bs-image="{{.CoverImage}}"
                     data-bs-title="{{.Title}}"
                     style="cursor: pointer;">
            </div>
            {{end}}
        </div>
    </div>

    <!-- 相关作品推荐 -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">相关作品</h3>
            <div class="text-center">
                {{if .Category}}
                <a href="/category/{{.Category.ID}}" class="btn btn-primary">
                    查看更多 {{.Category.Name}} 作品
                </a>
                {{else}}
                <a href="/" class="btn btn-primary">
                    查看所有作品
                </a>
                {{end}}
            </div>
        </div>
    </div>
    {{end}}
</div>

<!-- 图片模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">图片预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img src="" class="img-fluid" id="modalImage" alt="">
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
// 图片模态框
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('imageModalLabel');
    
    // 监听模态框显示事件
    imageModal.addEventListener('show.bs.modal', function(event) {
        const trigger = event.relatedTarget;
        const imageSrc = trigger.getAttribute('data-bs-image');
        const imageTitle = trigger.getAttribute('data-bs-title');
        
        modalImage.src = imageSrc;
        modalTitle.textContent = imageTitle;
    });
    
    // 缩略图点击事件
    const thumbs = document.querySelectorAll('.portfolio-thumb');
    thumbs.forEach(thumb => {
        thumb.addEventListener('click', function() {
            // 移除其他缩略图的激活状态
            thumbs.forEach(t => t.classList.remove('border-primary'));
            // 添加当前缩略图的激活状态
            this.classList.add('border-primary');
        });
    });
});
</script>
{{end}}
