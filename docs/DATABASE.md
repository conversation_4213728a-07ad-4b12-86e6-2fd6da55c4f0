# 数据库设计与配置

## 数据库选择

本项目支持两种数据库：**MySQL** 和 **SQLite**，可根据部署环境和需求选择。

### MySQL vs SQLite 对比

| 特性 | MySQL | SQLite |
|------|-------|--------|
| **部署复杂度** | 需要独立服务 | 无需独立服务，文件数据库 |
| **性能** | 高并发性能好 | 轻量级，适合小型应用 |
| **数据类型** | 丰富的数据类型支持 | 基本数据类型支持 |
| **并发支持** | 优秀的并发读写 | 读并发好，写并发有限 |
| **维护成本** | 需要专业维护 | 几乎无维护成本 |
| **适用场景** | 生产环境，高并发 | 开发环境，小型项目 |

### 推荐使用场景

- **开发环境**: 推荐使用 SQLite，快速启动，无需额外配置
- **生产环境**: 推荐使用 MySQL，更好的性能和稳定性
- **小型项目**: SQLite 足够使用
- **大型项目**: MySQL 更适合

## 数据库表结构

### MySQL 版本

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    avatar VARCHAR(255),
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 作品表
CREATE TABLE portfolios (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content LONGTEXT,
    cover_image VARCHAR(255),
    images JSON COMMENT '存储图片URL数组的JSON格式',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    status ENUM('draft', 'published') DEFAULT 'draft',
    view_count INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 作品图片表（可选，如果需要更复杂的图片管理）
CREATE TABLE portfolio_images (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    portfolio_id BIGINT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    alt_text VARCHAR(200),
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_portfolios_status ON portfolios(status);
CREATE INDEX idx_portfolios_created_at ON portfolios(created_at DESC);
CREATE INDEX idx_portfolios_sort_order ON portfolios(sort_order DESC);
CREATE INDEX idx_portfolios_view_count ON portfolios(view_count DESC);
CREATE INDEX idx_portfolio_images_portfolio_id ON portfolio_images(portfolio_id);
```

### SQLite 版本

```sql
-- 用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    email TEXT,
    avatar TEXT,
    role TEXT DEFAULT 'user' CHECK(role IN ('admin', 'user')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 作品表
CREATE TABLE portfolios (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    content TEXT,
    cover_image TEXT,
    images TEXT, -- 存储JSON格式的图片URL数组
    tags TEXT, -- 标签，逗号分隔
    status TEXT DEFAULT 'draft' CHECK(status IN ('draft', 'published')),
    view_count INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 作品图片表（可选）
CREATE TABLE portfolio_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    portfolio_id INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    alt_text TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_portfolios_status ON portfolios(status);
CREATE INDEX idx_portfolios_created_at ON portfolios(created_at DESC);
CREATE INDEX idx_portfolios_sort_order ON portfolios(sort_order DESC);
CREATE INDEX idx_portfolios_view_count ON portfolios(view_count DESC);
CREATE INDEX idx_portfolio_images_portfolio_id ON portfolio_images(portfolio_id);

-- SQLite触发器用于自动更新updated_at字段
CREATE TRIGGER update_users_updated_at 
    AFTER UPDATE ON users
    FOR EACH ROW
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_portfolios_updated_at 
    AFTER UPDATE ON portfolios
    FOR EACH ROW
    BEGIN
        UPDATE portfolios SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
```

## Go代码配置

### 数据库连接配置

```go
// config/database.go
package config

import (
    "fmt"
    "gorm.io/driver/mysql"
    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
)

type DatabaseConfig struct {
    Type     string `mapstructure:"type"`     // mysql 或 sqlite
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    User     string `mapstructure:"user"`
    Password string `mapstructure:"password"`
    DBName   string `mapstructure:"dbname"`
    FilePath string `mapstructure:"filepath"` // SQLite文件路径
}

func InitDatabase(config *DatabaseConfig) (*gorm.DB, error) {
    var db *gorm.DB
    var err error

    switch config.Type {
    case "mysql":
        dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
            config.User, config.Password, config.Host, config.Port, config.DBName)
        db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
    case "sqlite":
        db, err = gorm.Open(sqlite.Open(config.FilePath), &gorm.Config{})
    default:
        return nil, fmt.Errorf("unsupported database type: %s", config.Type)
    }

    if err != nil {
        return nil, err
    }

    return db, nil
}
```

### 环境变量配置

#### MySQL配置 (.env)
```env
# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=portfolio
```

#### SQLite配置 (.env)
```env
# 数据库配置
DB_TYPE=sqlite
DB_FILEPATH=./data/portfolio.db
```

### 数据模型定义

```go
// model/user.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID        uint           `gorm:"primaryKey" json:"id"`
    Username  string         `gorm:"uniqueIndex;size:50" json:"username"`
    Password  string         `gorm:"size:255" json:"-"`
    Email     string         `gorm:"size:100" json:"email"`
    Avatar    string         `gorm:"size:255" json:"avatar"`
    Role      string         `gorm:"size:20;default:'user'" json:"role"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// model/portfolio.go
package model

import (
    "database/sql/driver"
    "encoding/json"
    "time"
    "gorm.io/gorm"
)

// JSON类型用于存储图片URL数组
type JSON []string

func (j JSON) Value() (driver.Value, error) {
    return json.Marshal(j)
}

func (j *JSON) Scan(value interface{}) error {
    if value == nil {
        *j = nil
        return nil
    }
    
    bytes, ok := value.([]byte)
    if !ok {
        return fmt.Errorf("cannot scan %T into JSON", value)
    }
    
    return json.Unmarshal(bytes, j)
}

type Portfolio struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    Title       string         `gorm:"size:200" json:"title"`
    Description string         `gorm:"type:text" json:"description"`
    Content     string         `gorm:"type:longtext" json:"content"`
    CoverImage  string         `gorm:"size:255" json:"cover_image"`
    Images      JSON           `gorm:"type:json" json:"images"`
    Tags        string         `gorm:"size:500" json:"tags"`
    Status      string         `gorm:"size:20;default:'draft'" json:"status"`
    ViewCount   int            `gorm:"default:0" json:"view_count"`
    SortOrder   int            `gorm:"default:0" json:"sort_order"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// model/portfolio_image.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type PortfolioImage struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    PortfolioID uint           `gorm:"not null" json:"portfolio_id"`
    ImageURL    string         `gorm:"size:255" json:"image_url"`
    AltText     string         `gorm:"size:200" json:"alt_text"`
    SortOrder   int            `gorm:"default:0" json:"sort_order"`
    CreatedAt   time.Time      `json:"created_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
    
    // 关联
    Portfolio Portfolio `gorm:"foreignKey:PortfolioID" json:"-"`
}
```

## 数据库迁移

### 自动迁移
```go
// 在应用启动时执行
func AutoMigrate(db *gorm.DB) error {
    return db.AutoMigrate(
        &model.User{},
        &model.Portfolio{},
        &model.PortfolioImage{},
    )
}
```

### 初始数据
```go
// 创建默认管理员用户
func CreateDefaultAdmin(db *gorm.DB) error {
    var count int64
    db.Model(&model.User{}).Where("role = ?", "admin").Count(&count)
    
    if count == 0 {
        admin := &model.User{
            Username: "admin",
            Password: "$2a$10$...", // 加密后的密码
            Email:    "<EMAIL>",
            Role:     "admin",
        }
        return db.Create(admin).Error
    }
    
    return nil
}
```

## 部署建议

### 开发环境
- 使用SQLite，快速启动
- 数据文件放在项目目录下的`data/`文件夹

### 生产环境
- 使用MySQL，更好的性能和稳定性
- 配置数据库连接池
- 定期备份数据库
- 监控数据库性能

### Docker部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    environment:
      - DB_TYPE=mysql
      - DB_HOST=db
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: portfolio
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

volumes:
  mysql_data:
```

这样的设计既保证了灵活性，又满足了不同环境的需求。
